import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:audioplayers/audioplayers.dart';
import 'tafseer_screen.dart';
import 'translation_screen.dart';
import 'quran_settings_screen.dart';

class SurahDetailScreen extends StatefulWidget {
  final int surahNumber;
  final String surahName;
  final int? initialAyah;

  const SurahDetailScreen({
    super.key,
    required this.surahNumber,
    required this.surahName,
    this.initialAyah,
  });

  @override
  State<SurahDetailScreen> createState() => _SurahDetailScreenState();
}

class _SurahDetailScreenState extends State<SurahDetailScreen> {
  List<dynamic> ayahs = [];
  bool isLoading = true;
  String errorMessage = '';
  double _quranFontSize = 20.0;
  bool _showTranslation = false;
  bool _showTafseer = false;
  String _selectedTranslation = 'English_Sahih';
  String _selectedTafseer = '<PERSON>_<PERSON><PERSON>';
  bool _autoPlay = false;
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;
  int _currentAyahIndex = 0;
  List<dynamic> favoriteVerses = [];

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadSurah();
    _loadFavorites();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _quranFontSize = prefs.getDouble('quranFontSize') ?? 20.0;
      _showTranslation = prefs.getBool('showTranslation') ?? false;
      _showTafseer = prefs.getBool('showTafseer') ?? false;
      _selectedTranslation =
          prefs.getString('selectedTranslation') ?? 'English_Sahih';
      _selectedTafseer = prefs.getString('selectedTafseer') ?? 'Ibn_Kathir';
      _autoPlay = prefs.getBool('autoPlay') ?? false;
    });
  }

  Future<void> _loadSurah() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = '';
      });

      final response = await http.get(
        Uri.parse('https://api.alquran.cloud/v1/surah/${widget.surahNumber}'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          ayahs = data['data']['ayahs'];
          isLoading = false;

          // إذا تم تحديد آية محددة، انتقل إليها
          if (widget.initialAyah != null) {
            _scrollToAyah(widget.initialAyah!);
          }
        });
      } else {
        setState(() {
          errorMessage = 'خطأ في تحميل السورة';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'خطأ: ${e.toString()}';
        isLoading = false;
      });
    }
  }

  Future<void> _loadFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final String? favoritesString = prefs.getString('favorite_verses');

    if (favoritesString != null) {
      setState(() {
        favoriteVerses = json.decode(favoritesString);
      });
    }
  }

  Future<void> _saveFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('favorite_verses', json.encode(favoriteVerses));
  }

  void _scrollToAyah(int ayahNumber) {
    // البحث عن فهرس الآية المحددة
    final index =
        ayahs.indexWhere((ayah) => ayah['numberInSurah'] == ayahNumber);

    if (index != -1) {
      // تحديث الفهرس الحالي
      setState(() {
        _currentAyahIndex = index;
      });

      // التمرير إلى الآية بعد بناء الواجهة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Scrollable.ensureVisible(
          context,
          alignment: 0.5,
        );
      });
    }
  }

  void _toggleFavorite(int ayahNumber, String ayahText) {
    setState(() {
      final index = favoriteVerses.indexWhere((fav) =>
          fav['surahNumber'] == widget.surahNumber &&
          fav['ayahNumber'] == ayahNumber);

      if (index != -1) {
        // إزالة من المفضلة
        favoriteVerses.removeAt(index);
      } else {
        // إضافة إلى المفضلة
        favoriteVerses.add({
          'surahNumber': widget.surahNumber,
          'surahName': widget.surahName,
          'ayahNumber': ayahNumber,
          'ayahText': ayahText,
          'dateAdded': DateTime.now().toIso8601String(),
        });
      }

      _saveFavorites();
    });
  }

  bool _isAyahFavorite(int ayahNumber) {
    return favoriteVerses.any((fav) =>
        fav['surahNumber'] == widget.surahNumber &&
        fav['ayahNumber'] == ayahNumber);
  }

  Future<void> _playAyahAudio(int ayahNumber) async {
    try {
      setState(() {
        _isPlaying = true;
      });

      // استخدام API لتشغيل صوت الآية
      await _audioPlayer.play(
        UrlSource(
            'https://cdn.islamic.network/quran/audio/128/ar.alafasy/$ayahNumber.mp3'),
      );

      // عند انتهاء التشغيل
      _audioPlayer.onPlayerComplete.listen((_) {
        setState(() {
          _isPlaying = false;
        });

        // إذا كان التشغيل التلقائي مفعلًا، انتقل للآية التالية
        if (_autoPlay && _currentAyahIndex < ayahs.length - 1) {
          _playNextAyah();
        }
      });
    } catch (e) {
      setState(() {
        _isPlaying = false;
      });
      debugPrint('خطأ في تشغيل الصوت: $e');
    }
  }

  void _playNextAyah() {
    if (_currentAyahIndex < ayahs.length - 1) {
      setState(() {
        _currentAyahIndex++;
      });

      final nextAyah = ayahs[_currentAyahIndex];
      _playAyahAudio(nextAyah['number']);

      // التمرير إلى الآية التالية
      _scrollToAyah(nextAyah['numberInSurah']);
    }
  }

  void _playPreviousAyah() {
    if (_currentAyahIndex > 0) {
      setState(() {
        _currentAyahIndex--;
      });

      final prevAyah = ayahs[_currentAyahIndex];
      _playAyahAudio(prevAyah['number']);

      // التمرير إلى الآية السابقة
      _scrollToAyah(prevAyah['numberInSurah']);
    }
  }

  void _stopAudio() {
    _audioPlayer.stop();
    setState(() {
      _isPlaying = false;
    });
  }

  void _showAyahOptions(int ayahNumber, String ayahText) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF5C3A21).withOpacity(0.87),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              ListTile(
                leading: Icon(
                  _isAyahFavorite(ayahNumber)
                      ? Icons.favorite
                      : Icons.favorite_border,
                  color: _isAyahFavorite(ayahNumber)
                      ? const Color(0xFF5C3A21)
                      : Colors.white,
                ),
                title: Text(
                  _isAyahFavorite(ayahNumber)
                      ? 'إزالة من المفضلة'
                      : 'إضافة إلى المفضلة',
                  style: const TextStyle(color: Colors.white),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _toggleFavorite(ayahNumber, ayahText);
                },
              ),
              ListTile(
                leading: const Icon(Icons.share, color: Colors.white),
                title: const Text(
                  'مشاركة الآية',
                  style: TextStyle(color: Colors.white),
                ),
                onTap: () {
                  Navigator.pop(context);
                  // Implement share functionality
                },
              ),
              ListTile(
                leading: const Icon(Icons.copy, color: Colors.white),
                title: const Text(
                  'نسخ الآية',
                  style: TextStyle(color: Colors.white),
                ),
                onTap: () {
                  Navigator.pop(context);
                  Clipboard.setData(ClipboardData(text: ayahText));
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم نسخ الآية')),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.translate, color: Colors.white),
                title: const Text(
                  'عرض الترجمة',
                  style: TextStyle(color: Colors.white),
                ),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => TranslationScreen(
                        surahNumber: widget.surahNumber,
                        ayahNumber: ayahNumber,
                        translationName: _selectedTranslation,
                      ),
                    ),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.menu_book, color: Colors.white),
                title: const Text(
                  'عرض التفسير',
                  style: TextStyle(color: Colors.white),
                ),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => TafseerScreen(
                        surahNumber: widget.surahNumber,
                        ayahNumber: ayahNumber,
                        tafseerName: _selectedTafseer,
                      ),
                    ),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.play_circle, color: Colors.white),
                title: const Text(
                  'تشغيل الصوت',
                  style: TextStyle(color: Colors.white),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _playAyahAudio(ayahNumber);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.surahName,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF5C3A21),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          // زر التشغيل الكامل
          IconButton(
            icon: Icon(_isPlaying ? Icons.stop_circle : Icons.play_circle),
            onPressed: () {
              if (_isPlaying) {
                _stopAudio();
              } else {
                _playAyahAudio(ayahs[0]['number']);
              }
            },
          ),
          // زر الإعدادات
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const QuranSettingsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF5C3A21), Color(0xFF5C3A21)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            // شريط التحكم في التشغيل
            if (_isPlaying)
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFF5C3A21).withOpacity(0.3),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    IconButton(
                      icon:
                          const Icon(Icons.skip_previous, color: Colors.white),
                      onPressed: _playPreviousAyah,
                    ),
                    IconButton(
                      icon: const Icon(Icons.stop, color: Colors.white),
                      onPressed: _stopAudio,
                    ),
                    IconButton(
                      icon: const Icon(Icons.skip_next, color: Colors.white),
                      onPressed: _playNextAyah,
                    ),
                  ],
                ),
              ),

            // قائمة الآيات
            Expanded(
              child: isLoading
                  ? const Center(
                      child: CircularProgressIndicator(
                        valueColor:
                            AlwaysStoppedAnimation<Color>(Color(0xFF5C3A21)),
                      ),
                    )
                  : errorMessage.isNotEmpty
                      ? Center(
                          child: Text(
                            errorMessage,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Color(0xFF5C3A21),
                            ),
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: ayahs.length,
                          itemBuilder: (context, index) {
                            final ayah = ayahs[index];
                            final isCurrentAyah = index == _currentAyahIndex;

                            return Container(
                              key: Key('ayah_${ayah['numberInSurah']}'),
                              margin: const EdgeInsets.only(bottom: 16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: isCurrentAyah
                                      ? [
                                          const Color(0xFF5C3A21),
                                          const Color(0xFF5C3A21)
                                        ]
                                      : [
                                          const Color(0xFF5C3A21),
                                          const Color(0xFF5C3A21)
                                        ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: isCurrentAyah
                                      ? const Color(0xFFD4B899)
                                      : const Color(0xFFD4B899)
                                          .withOpacity(0.5),
                                  width: isCurrentAyah ? 2 : 1,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        // أزرار الإجراءات
                                        Row(
                                          children: [
                                            // زر التفسير
                                            if (_showTafseer)
                                              IconButton(
                                                icon: const Icon(
                                                  Icons.menu_book,
                                                  color: Colors.white70,
                                                  size: 20,
                                                ),
                                                onPressed: () {
                                                  Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (context) =>
                                                          TafseerScreen(
                                                        surahNumber:
                                                            widget.surahNumber,
                                                        ayahNumber: ayah[
                                                            'numberInSurah'],
                                                        tafseerName:
                                                            _selectedTafseer,
                                                      ),
                                                    ),
                                                  );
                                                },
                                              ),

                                            // زر الترجمة
                                            if (_showTranslation)
                                              IconButton(
                                                icon: const Icon(
                                                  Icons.translate,
                                                  color: Colors.white70,
                                                  size: 20,
                                                ),
                                                onPressed: () {
                                                  Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (context) =>
                                                          TranslationScreen(
                                                        surahNumber:
                                                            widget.surahNumber,
                                                        ayahNumber: ayah[
                                                            'numberInSurah'],
                                                        translationName:
                                                            _selectedTranslation,
                                                      ),
                                                    ),
                                                  );
                                                },
                                              ),

                                            // زر المفضلة
                                            IconButton(
                                              icon: Icon(
                                                _isAyahFavorite(
                                                        ayah['numberInSurah'])
                                                    ? Icons.favorite
                                                    : Icons.favorite_border,
                                                color: _isAyahFavorite(
                                                        ayah['numberInSurah'])
                                                    ? const Color(0xFF5C3A21)
                                                    : Colors.white70,
                                                size: 20,
                                              ),
                                              onPressed: () {
                                                _toggleFavorite(
                                                  ayah['numberInSurah'],
                                                  ayah['text'],
                                                );
                                              },
                                            ),

                                            // زر القائمة
                                            IconButton(
                                              icon: const Icon(
                                                Icons.more_vert,
                                                color: Colors.white70,
                                                size: 20,
                                              ),
                                              onPressed: () {
                                                _showAyahOptions(
                                                  ayah['numberInSurah'],
                                                  ayah['text'],
                                                );
                                              },
                                            ),
                                          ],
                                        ),

                                        // رقم الآية
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: const Color(0xFF5C3A21)
                                                .withOpacity(0.1),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Text(
                                            'آية ${ayah['numberInSurah']}',
                                            style: const TextStyle(
                                              fontSize: 12,
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 12),

                                    // نص الآية
                                    Text(
                                      ayah['text'],
                                      style: TextStyle(
                                        fontSize: _quranFontSize,
                                        height: 2.0,
                                        color: Colors.white,
                                      ),
                                      textAlign: TextAlign.right,
                                      textDirection: TextDirection.rtl,
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
            ),
          ],
        ),
      ),
    );
  }
}
