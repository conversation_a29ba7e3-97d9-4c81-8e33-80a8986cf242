import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:audioplayers/audioplayers.dart';
import 'quran_settings_screen.dart';
import 'tafseer_screen.dart';
import 'translation_screen.dart';
import '../data/reciters_data.dart';
import 'package:man_yakhaf_waeid/features/mushaf_screen.dart';

class QuranScreen extends StatefulWidget {
  final String surahName;
  final int surahNumber;

  const QuranScreen({
    super.key,
    required this.surahName,
    required this.surahNumber,
  });

  @override
  State<QuranScreen> createState() => _QuranScreenState();
}

class _QuranScreenState extends State<QuranScreen> {
  List<dynamic> surahs = [];
  bool isLoading = true;
  String errorMessage = '';
  List<dynamic> favoriteVerses = [];
  List<dynamic> searchResults = [];
  double dailyProgress = 0.0;
  int currentPage = 1;
  int targetPages = 20;

  @override
  void initState() {
    super.initState();
    _loadSurahs();
    _loadFavorites();
    _loadDailyProgress();
  }

  Future<void> _loadSurahs() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = '';
      });

      final response = await http.get(
        Uri.parse('https://api.alquran.cloud/v1/surah'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          surahs = data['data'];
          isLoading = false;
        });
      } else {
        setState(() {
          errorMessage = 'خطأ في تحميل السور';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'خطأ: ${e.toString()}';
        isLoading = false;
      });
    }
  }

  Future<void> _loadFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesString = prefs.getString('favorite_verses');
    if (favoritesString != null) {
      setState(() {
        favoriteVerses = json.decode(favoritesString);
      });
    } else {
      setState(() {
        favoriteVerses = [];
      });
    }
  }

  Future<void> _loadDailyProgress() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      dailyProgress = prefs.getDouble('daily_progress') ?? 0.0;
      currentPage = prefs.getInt('current_page') ?? 1;
      targetPages = prefs.getInt('target_pages') ?? 20;
    });
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        String query = '';
        List<dynamic> searchResults = [];

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('البحث في القرآن'),
              content: SizedBox(
                width: double.maxFinite,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      decoration: InputDecoration(
                        hintText: 'أدخل كلمة البحث...',
                        prefixIcon: Image.asset(
                          'assets/icons/search_icon.png',
                          width: 24,
                          height: 24,
                        ),
                      ),
                      onChanged: (value) {
                        query = value;
                      },
                      onSubmitted: (value) async {
                        if (value.isNotEmpty) {
                          await _performSearch(value, setState);
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    if (searchResults.isNotEmpty)
                      Expanded(
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: searchResults.length,
                          itemBuilder: (context, index) {
                            final result = searchResults[index];
                            return ListTile(
                              title: Text(
                                '${result['surahName']}: آية ${result['ayahNumber']}',
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                              subtitle: Text(
                                result['text'],
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              onTap: () {
                                Navigator.pop(context);
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => SurahDetailScreen(
                                      surahNumber: result['surahNumber'],
                                      surahName: result['surahName'],
                                      initialAyah: result['ayahNumber'],
                                    ),
                                  ),
                                ).then((_) => _loadFavorites());
                              },
                            );
                          },
                        ),
                      ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إلغاء'),
                ),
                TextButton(
                  onPressed: () async {
                    if (query.isNotEmpty) {
                      await _performSearch(query, setState);
                    }
                  },
                  child: const Text('بحث'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _performSearch(String query, StateSetter setState) async {
    try {
      final response = await http.get(
        Uri.parse('https://api.alquran.cloud/v1/search/$query/all/ar'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          // تحويل نتائج البحث إلى قائمة
          searchResults = data['data']['matches'].map((match) {
            return {
              'text': match['text'],
              'surahNumber': match['surah']['number'],
              'surahName': match['surah']['name'],
              'ayahNumber': match['numberInSurah'],
            };
          }).toList();
        });
      }
    } catch (e) {
      debugPrint('خطأ في البحث: $e');
    }
  }

  // بناء بطاقة التقدم اليومي
  Widget _buildDailyProgressCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [const Color(0xFF5C3A21), const Color(0xFF5C3A21)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'التقدم اليومي',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    LinearProgressIndicator(
                      value: currentPage / targetPages,
                      backgroundColor: const Color(0xFF5C3A21).withOpacity(0.3),
                      valueColor:
                          const AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'صفحة $currentPage من $targetPages',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Image.asset(
                'assets/icons/favorites_icon.png',
                width: 24,
                height: 24,
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'القرآن الكريم',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF5C3A21),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: Image.asset(
              'assets/icons/search_icon.png',
              width: 24,
              height: 24,
            ),
            onPressed: () {
              _showSearchDialog();
            },
          ),
          IconButton(
            icon: Image.asset(
              'assets/icons/settings_icon.png',
              width: 24,
              height: 24,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const QuranSettingsScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: Image.asset(
              'assets/icons/quran_icon.png',
              width: 24,
              height: 24,
            ), // Using quran_icon for Mushaf view
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MushafScreen(),
                ),
              );
            },
            tooltip: 'عرض المصحف',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF5C3A21), Color(0xFF5C3A21)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: isLoading
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(Color(0xFF5C3A21)),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'جاري تحميل سور القرآن الكريم...',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              )
            : errorMessage.isNotEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Color(0xFF5C3A21),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          errorMessage,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Color(0xFF5C3A21),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        OutlinedButton(
                          onPressed: _loadSurahs,
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(
                                color: Color(0xFFD4B899), width: 2),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30.0),
                            ),
                          ),
                          child: const Text(
                            'إعادة المحاولة',
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      ],
                    ),
                  )
                : Column(
                    children: [
                      // جزء لعرض التقدم اليومي
                      _buildDailyProgressCard(),

                      const SizedBox(height: 16),

                      // قائمة السور
                      Expanded(
                        child: ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: surahs.length,
                          itemBuilder: (context, index) {
                            final surah = surahs[index];
                            return GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => SurahDetailScreen(
                                      surahNumber: surah['number'],
                                      surahName: surah['name'],
                                    ),
                                  ),
                                ).then((_) => _loadFavorites());
                              },
                              child: Container(
                                margin: const EdgeInsets.only(bottom: 16),
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    colors: [
                                      Color(0xFF5C3A21),
                                      Color(0xFF5C3A21)
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: const Color(0xFFD4B899),
                                    width: 2,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              surah['name'],
                                              style: const TextStyle(
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                            Text(
                                              '${surah['englishName']} - ${surah['numberOfAyahs']} آيات',
                                              style: const TextStyle(
                                                fontSize: 14,
                                                color: Colors.white70,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Container(
                                        width: 50,
                                        height: 50,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          gradient: LinearGradient(
                                            colors: [
                                              const Color(0xFF5C3A21)
                                                  .withOpacity(0.8),
                                              const Color(0xFF5C3A21)
                                                  .withOpacity(0.8)
                                            ],
                                            begin: Alignment.topLeft,
                                            end: Alignment.bottomRight,
                                          ),
                                        ),
                                        child: Center(
                                          child: Text(
                                            '${surah['number']}',
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
      ),
    );
  }
}

class SurahDetailScreen extends StatefulWidget {
  final int surahNumber;
  final String surahName;
  final int? initialAyah;

  const SurahDetailScreen({
    super.key,
    required this.surahNumber,
    required this.surahName,
    this.initialAyah,
  });

  @override
  State<SurahDetailScreen> createState() => _SurahDetailScreenState();
}

class _SurahDetailScreenState extends State<SurahDetailScreen> {
  List<dynamic> ayahs = [];
  List<dynamic> favoriteVerses = [];
  bool isLoading = true;
  String errorMessage = '';
  double _quranFontSize = 20.0;
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _showTranslation = false;
  bool _showTafseer = false;
  String _selectedTranslation = 'English_Sahih';
  String _selectedTafseer = 'Ibn_Kathir';
  bool _autoPlay = false;
  int _currentAyahIndex = 0;
  bool _isPlaying = false;
  String _selectedReciterBaseUrl =
      reciters[0]['baseUrl']!; // Default to Alafasy 128kbps

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadSurah();
    _loadFavorites();

    if (widget.initialAyah != null) {
      // إذا تم تحديد آية محددة، سيتم التمرير إليها
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToAyah(widget.initialAyah!);
      });
    }
  }

  Future<void> _loadFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesString = prefs.getString('favorite_verses');
    if (favoritesString != null) {
      setState(() {
        favoriteVerses = json.decode(favoritesString);
      });
    }
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _quranFontSize = prefs.getDouble('quranFontSize') ?? 20.0;
      _showTranslation = prefs.getBool('showTranslation') ?? false;
      _showTafseer = prefs.getBool('showTafseer') ?? false;
      _selectedTranslation =
          prefs.getString('selectedTranslation') ?? 'English_Sahih';
      _selectedTafseer = prefs.getString('selectedTafseer') ?? 'Ibn_Kathir';
      _autoPlay = prefs.getBool('autoPlay') ?? false;
      _selectedReciterBaseUrl =
          prefs.getString('selectedReciterBaseUrl') ?? reciters[0]['baseUrl']!;
    });
  }

  Future<void> _loadSurah() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = '';
      });

      final response = await http.get(
        Uri.parse('https://api.alquran.cloud/v1/surah/${widget.surahNumber}'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          ayahs = data['data']['ayahs'];
          isLoading = false;
        });
      } else {
        setState(() {
          errorMessage = 'خطأ في تحميل السورة';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'خطأ: ${e.toString()}';
        isLoading = false;
      });
    }
  }

  void _scrollToAyah(int ayahNumber) {
    final index =
        ayahs.indexWhere((ayah) => ayah['numberInSurah'] == ayahNumber);
    if (index != -1) {
      setState(() {
        _currentAyahIndex = index;
      });
    }
  }

  Future<void> _playAyahAudio(int ayahNumber) async {
    try {
      // إيقاف أي تشغيل حالي
      await _audioPlayer.stop();

      setState(() {
        _isPlaying = true;
      });

      // تشغيل الصوت
      final surahPadded = widget.surahNumber.toString().padLeft(3, '0');
      final ayahPadded = ayahNumber.toString().padLeft(3, '0');
      await _audioPlayer.play(
        UrlSource('$_selectedReciterBaseUrl$surahPadded$ayahPadded.mp3'),
      );

      // الاستماع لانتهاء التشغيل
      _audioPlayer.onPlayerComplete.listen((_) {
        setState(() {
          _isPlaying = false;
        });

        // إذا كان التشغيل التلقائي مفعلًا والآية الحالية ليست الأخيرة
        if (_autoPlay && _currentAyahIndex < ayahs.length - 1) {
          setState(() {
            _currentAyahIndex++;
          });
          _playAyahAudio(ayahs[_currentAyahIndex]['numberInSurah']);
        }
      });
    } catch (e) {
      setState(() {
        _isPlaying = false;
      });
      debugPrint('خطأ في تشغيل الصوت: $e');
    }
  }

  Future<void> _toggleFavorite(int ayahNumber, String ayahText) async {
    final prefs = await SharedPreferences.getInstance();

    // تحميل المفضلات الحالية
    final String? favoritesString = prefs.getString('favorite_verses');
    List<dynamic> favorites = [];
    if (favoritesString != null) {
      favorites = json.decode(favoritesString);
    }

    // التحقق إذا كانت الآية موجودة بالفعل
    final index = favorites.indexWhere((item) =>
        item['surahNumber'] == widget.surahNumber &&
        item['ayahNumber'] == ayahNumber);

    if (index != -1) {
      // إذا كانت موجودة، قم بإزالتها
      favorites.removeAt(index);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تمت إزالة الآية من المفضلة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      // إذا لم تكن موجودة، قم بإضافتها
      favorites.add({
        'surahNumber': widget.surahNumber,
        'surahName': widget.surahName,
        'ayahNumber': ayahNumber,
        'ayahText': ayahText,
        'dateAdded': DateTime.now().toIso8601String(),
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تمت إضافة الآية إلى المفضلة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }

    // حفظ القائمة المحدثة
    await prefs.setString('favorite_verses', json.encode(favorites));

    // تحديث الواجهة
    setState(() {
      favoriteVerses = favorites;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.surahName,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF1976D2),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          // زر التشغيل التلقائي
          IconButton(
            icon:
                Icon(_autoPlay ? Icons.play_circle : Icons.play_circle_outline),
            onPressed: () {
              setState(() {
                _autoPlay = !_autoPlay;
              });
            },
          ),
          // زر الإعدادات
          IconButton(
            icon: Image.asset(
              'assets/icons/settings_icon.png',
              width: 24,
              height: 24,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const QuranSettingsScreen(),
                ),
              ).then((_) {
                _loadSettings();
              });
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF0D47A1), Colors.black],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: isLoading
            ? const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1976D2)),
                ),
              )
            : errorMessage.isNotEmpty
                ? Center(
                    child: Text(
                      errorMessage,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.red,
                      ),
                    ),
                  )
                : Column(
                    children: [
                      // شريط التحكم في التشغيل
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.3),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            // زر الآية السابقة
                            IconButton(
                              icon: const Icon(Icons.skip_previous,
                                  color: Colors.white),
                              onPressed: _currentAyahIndex > 0
                                  ? () {
                                      setState(() {
                                        _currentAyahIndex--;
                                      });
                                    }
                                  : null,
                            ),

                            // زر التشغيل/الإيقاف
                            IconButton(
                              icon: Icon(
                                _isPlaying ? Icons.pause : Icons.play_arrow,
                                color: Colors.white,
                                size: 32,
                              ),
                              onPressed: () {
                                if (_isPlaying) {
                                  _audioPlayer.pause();
                                } else {
                                  _playAyahAudio(ayahs[_currentAyahIndex]
                                      ['numberInSurah']);
                                }
                                setState(() {
                                  _isPlaying = !_isPlaying;
                                });
                              },
                            ),

                            // زر الآية التالية
                            IconButton(
                              icon: const Icon(Icons.skip_next,
                                  color: Colors.white),
                              onPressed: _currentAyahIndex < ayahs.length - 1
                                  ? () {
                                      setState(() {
                                        _currentAyahIndex++;
                                      });
                                    }
                                  : null,
                            ),
                          ],
                        ),
                      ),

                      // قائمة الآيات
                      Expanded(
                        child: ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: ayahs.length,
                          itemBuilder: (context, index) {
                            final ayah = ayahs[index];
                            final bool isCurrentAyah =
                                index == _currentAyahIndex;

                            return Container(
                              margin: const EdgeInsets.only(bottom: 16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: isCurrentAyah
                                      ? [
                                          const Color(0xFF2E7D32),
                                          const Color(0xFF1976D2)
                                        ]
                                      : [const Color(0xFF0D47A1), Colors.black],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: isCurrentAyah
                                      ? const Color(0xFF4CAF50)
                                      : const Color(0xFFFFD700),
                                  width: 2,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        // أزرار الإجراءات
                                        Row(
                                          children: [
                                            // زر التشغيل
                                            IconButton(
                                              icon: Icon(
                                                _isPlaying && isCurrentAyah
                                                    ? Icons.pause
                                                    : Icons.play_arrow,
                                                color: Colors.white,
                                                size: 20,
                                              ),
                                              onPressed: () {
                                                setState(() {
                                                  _currentAyahIndex = index;
                                                });

                                                if (_isPlaying &&
                                                    isCurrentAyah) {
                                                  _audioPlayer.pause();
                                                  setState(() {
                                                    _isPlaying = false;
                                                  });
                                                } else {
                                                  _playAyahAudio(
                                                      ayah['numberInSurah']);
                                                }
                                              },
                                            ),

                                            // زر المفضلة
                                            IconButton(
                                              icon: Image.asset(
                                                'assets/icons/favorites_icon.png',
                                                width: 20,
                                                height: 20,
                                              ),
                                              onPressed: () {
                                                _toggleFavorite(
                                                  ayah['numberInSurah'],
                                                  ayah['text'],
                                                );
                                              },
                                            ),

                                            // زر الترجمة
                                            if (_showTranslation)
                                              IconButton(
                                                icon: const Icon(
                                                  Icons.translate,
                                                  color: Colors.white,
                                                  size: 20,
                                                ),
                                                onPressed: () {
                                                  Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (context) =>
                                                          TranslationScreen(
                                                        surahNumber:
                                                            widget.surahNumber,
                                                        ayahNumber: ayah[
                                                            'numberInSurah'],
                                                        translationName:
                                                            _selectedTranslation,
                                                      ),
                                                    ),
                                                  );
                                                },
                                              ),

                                            // زر التفسير
                                            if (_showTafseer)
                                              IconButton(
                                                icon: Image.asset(
                                                  'assets/icons/learning_library_icon.png',
                                                  width: 20,
                                                  height: 20,
                                                ),
                                                onPressed: () {
                                                  Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (context) =>
                                                          TafseerScreen(
                                                        surahNumber:
                                                            widget.surahNumber,
                                                        ayahNumber: ayah[
                                                            'numberInSurah'],
                                                        tafseerName:
                                                            _selectedTafseer,
                                                      ),
                                                    ),
                                                  );
                                                },
                                              ),
                                          ],
                                        ),

                                        // رقم الآية
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: const Color(0xFF1976D2)
                                                .withValues(alpha: 0.1),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Text(
                                            'آية ${ayah['numberInSurah']}',
                                            style: const TextStyle(
                                              fontSize: 12,
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 12),

                                    // نص الآية
                                    Text(
                                      ayah['text'],
                                      style: TextStyle(
                                        fontSize: _quranFontSize,
                                        height: 2.0,
                                        color: Colors.white,
                                      ),
                                      textAlign: TextAlign.right,
                                      textDirection: TextDirection.rtl,
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
      ),
    );
  }
}
