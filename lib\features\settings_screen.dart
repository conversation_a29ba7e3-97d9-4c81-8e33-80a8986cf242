import 'package:flutter/material.dart';
import 'package:man_yakhaf_waeid/main.dart';
import 'package:man_yakhaf_waeid/themes.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String _calculationMethod = 'muslim_world_league';
  String _madhab = 'shafi';
  bool _notifications = true;
  bool _darkMode = false;
  String _primaryColorHex = '#5C3A21'; // Dark brown
  String _accentColorHex = '#D4B899'; // Light brown/beige
  bool _fajrNotification = true;
  bool _dhuhrNotification = true;
  bool _asrNotification = true;
  bool _maghribNotification = true;
  bool _ishaNotification = true;
  String _adhanSound = 'default';
  double _quranFontSize = 20.0;
  String _appFont = 'ScheherazadeNew';
  String _theme = 'light';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _calculationMethod =
          prefs.getString('calculationMethod') ?? 'muslim_world_league';
      _madhab = prefs.getString('madhab') ?? 'shafi';
      _notifications = prefs.getBool('notifications') ?? true;
      _darkMode = prefs.getBool('darkMode') ?? false;
      _primaryColorHex = prefs.getString('primaryColorHex') ?? '#2E7D32';
      _accentColorHex = prefs.getString('accentColorHex') ?? '#FFD700';
      _fajrNotification = prefs.getBool('fajrNotification') ?? true;
      _dhuhrNotification = prefs.getBool('dhuhrNotification') ?? true;
      _asrNotification = prefs.getBool('asrNotification') ?? true;
      _maghribNotification = prefs.getBool('maghribNotification') ?? true;
      _ishaNotification = prefs.getBool('ishaNotification') ?? true;
      _adhanSound = prefs.getString('adhanSound') ?? 'default';
      _quranFontSize = prefs.getDouble('quranFontSize') ?? 20.0;
      _appFont = prefs.getString('appFont') ?? 'ScheherazadeNew';
      _theme = prefs.getString('theme') ?? 'light';
    });
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('calculationMethod', _calculationMethod);
    await prefs.setString('madhab', _madhab);
    await prefs.setBool('notifications', _notifications);
    await prefs.setBool('darkMode', _darkMode);
    await prefs.setString('primaryColorHex', _primaryColorHex);
    await prefs.setString('accentColorHex', _accentColorHex);
    await prefs.setBool('fajrNotification', _fajrNotification);
    await prefs.setBool('dhuhrNotification', _dhuhrNotification);
    await prefs.setBool('asrNotification', _asrNotification);
    await prefs.setBool('maghribNotification', _maghribNotification);
    await prefs.setBool('ishaNotification', _ishaNotification);
    await prefs.setString('adhanSound', _adhanSound);
    await prefs.setDouble('quranFontSize', _quranFontSize);
    await prefs.setString('appFont', _appFont);
    await prefs.setString('theme', _theme);
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<ThemeData>(
      valueListenable: themeNotifier,
      builder: (context, theme, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text(
              'الإعدادات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            backgroundColor:
                _getColorFromHex(_primaryColorHex), // Use primary color
            iconTheme: const IconThemeData(color: Colors.white),
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: theme.brightness == Brightness.dark
                    ? [const Color(0xFF5C3A21), const Color(0xFF5C3A21)]
                    : [const Color(0xFFD4B899), const Color(0xFFD4B899)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                _buildSettingsCard(
                  'طريقة حساب أوقات الصلاة',
                  DropdownButton<String>(
                    value: _calculationMethod,
                    onChanged: (String? newValue) {
                      setState(() {
                        _calculationMethod = newValue!;
                        _saveSettings();
                      });
                    },
                    items: const [
                      DropdownMenuItem(
                        value: 'muslim_world_league',
                        child: Text('رابطة العالم الإسلامي'),
                      ),
                      DropdownMenuItem(
                        value: 'egyptian',
                        child: Text('الهيئة المصرية العامة للمساحة'),
                      ),
                      DropdownMenuItem(
                        value: 'karachi',
                        child: Text('جامعة العلوم الإسلامية بكراتشي'),
                      ),
                      DropdownMenuItem(
                        value: 'north_america',
                        child:
                            Text('اتحاد الجمعيات الإسلامية في أمريكا الشمالية'),
                      ),
                    ],
                  ),
                ),
                _buildSettingsCard(
                  'المذهب',
                  DropdownButton<String>(
                    value: _madhab,
                    onChanged: (String? newValue) {
                      setState(() {
                        _madhab = newValue!;
                        _saveSettings();
                      });
                    },
                    items: const [
                      DropdownMenuItem(
                        value: 'shafi',
                        child: Text('شافعي'),
                      ),
                      DropdownMenuItem(
                        value: 'hanafi',
                        child: Text('حنفي'),
                      ),
                    ],
                  ),
                ),
                _buildSettingsCard(
                  'الإشعارات',
                  Switch(
                    value: _notifications,
                    onChanged: (bool value) {
                      setState(() {
                        _notifications = value;
                        _saveSettings();
                      });
                    },
                  ),
                ),
                _buildSettingsCard(
                  'الوضع الداكن',
                  Switch(
                    value: _darkMode,
                    onChanged: (bool value) {
                      setState(() {
                        _darkMode = value;
                        _saveSettings();
                        themeNotifier.value = value ? darkTheme : lightTheme;
                        _theme = value ? 'dark' : 'light';
                      });
                    },
                  ),
                ),
                _buildColorPicker(
                  'اللون الأساسي',
                  _primaryColorHex,
                  (String colorHex) {
                    setState(() {
                      _primaryColorHex = colorHex;
                    });
                  },
                ),
                _buildColorPicker(
                  'لون التمييز',
                  _accentColorHex,
                  (String colorHex) {
                    setState(() {
                      _accentColorHex = colorHex;
                    });
                  },
                ),
                _buildSettingsCard(
                  'إشعارات الصلاة',
                  Column(
                    children: [
                      _buildNotificationSwitch('الفجر', _fajrNotification,
                          (value) {
                        setState(() {
                          _fajrNotification = value;
                          _saveSettings();
                        });
                      }),
                      _buildNotificationSwitch('الظهر', _dhuhrNotification,
                          (value) {
                        setState(() {
                          _dhuhrNotification = value;
                          _saveSettings();
                        });
                      }),
                      _buildNotificationSwitch('العصر', _asrNotification,
                          (value) {
                        setState(() {
                          _asrNotification = value;
                          _saveSettings();
                        });
                      }),
                      _buildNotificationSwitch('المغرب', _maghribNotification,
                          (value) {
                        setState(() {
                          _maghribNotification = value;
                          _saveSettings();
                        });
                      }),
                      _buildNotificationSwitch('العشاء', _ishaNotification,
                          (value) {
                        setState(() {
                          _ishaNotification = value;
                          _saveSettings();
                        });
                      }),
                    ],
                  ),
                ),
                _buildSettingsCard(
                  'صوت الأذان',
                  DropdownButton<String>(
                    value: _adhanSound,
                    onChanged: (String? newValue) {
                      setState(() {
                        _adhanSound = newValue!;
                        _saveSettings();
                      });
                    },
                    items: const [
                      DropdownMenuItem(
                        value: 'default',
                        child: Text('افتراضي'),
                      ),
                      DropdownMenuItem(
                        value: 'mishary_alafasy',
                        child: Text('مشاري العفاسي'),
                      ),
                      DropdownMenuItem(
                        value: 'abdul_basit',
                        child: Text('عبد الباسط عبد الصمد'),
                      ),
                    ],
                  ),
                ),
                _buildSettingsCard(
                  'حجم خط القرآن',
                  Slider(
                    value: _quranFontSize,
                    min: 12,
                    max: 40,
                    divisions: 28,
                    label: _quranFontSize.round().toString(),
                    onChanged: (double value) {
                      setState(() {
                        _quranFontSize = value;
                        _saveSettings();
                      });
                    },
                  ),
                ),
                _buildSettingsCard(
                  'خط التطبيق',
                  DropdownButton<String>(
                    value: _appFont,
                    onChanged: (String? newValue) {
                      setState(() {
                        _appFont = newValue!;
                        _saveSettings();
                        appFontNotifier.value = newValue;
                      });
                    },
                    items: const [
                      DropdownMenuItem(
                        value: 'Cairo',
                        child: Text('Cairo'),
                      ),
                      DropdownMenuItem(
                        value: 'Amiri',
                        child: Text('Amiri'),
                      ),
                      DropdownMenuItem(
                        value: 'ScheherazadeNew',
                        child: Text('Scheherazade New'),
                      ),
                      DropdownMenuItem(
                        value: 'Uthmanic',
                        child: Text('Uthmanic'),
                      ),
                      DropdownMenuItem(
                        value: 'UthmanicHafs',
                        child: Text('Uthmanic Hafs'),
                      ),
                    ],
                  ),
                ),
                _buildSettingsCard(
                  'الثيم',
                  DropdownButton<String>(
                    value: _theme,
                    onChanged: (String? newValue) {
                      setState(() {
                        _theme = newValue!;
                        _saveSettings();
                        themeNotifier.value =
                            newValue == 'light' ? lightTheme : darkTheme;
                      });
                    },
                    items: const [
                      DropdownMenuItem(
                        value: 'light',
                        child: Text('فاتح'),
                      ),
                      DropdownMenuItem(
                        value: 'dark',
                        child: Text('داكن'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Helper method to convert hex string to Color
  Color _getColorFromHex(String hexColor) {
    return Color(int.parse(hexColor.replaceAll('#', 'FF'), radix: 16));
  }

  Widget _buildColorPicker(
      String title, String currentColorHex, Function(String) onChanged) {
    final List<Map<String, String>> colorOptions = [
      {'name': 'Dark Brown', 'hex': '#5C3A21'},
      {'name': 'Light Brown', 'hex': '#D4B899'},
    ];

    return _buildSettingsCard(
      title,
      DropdownButton<String>(
        value: currentColorHex,
        onChanged: (String? newValue) {
          if (newValue != null) {
            onChanged(newValue);
            _saveSettings();
          }
        },
        items: colorOptions.map((colorMap) {
          return DropdownMenuItem<String>(
            value: colorMap['hex'],
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  color: _getColorFromHex(colorMap['hex']!),
                ),
                const SizedBox(width: 8),
                Text(colorMap['name']!),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildNotificationSwitch(
      String title, bool value, Function(bool) onChanged) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.white,
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildSettingsCard(String title, Widget child) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF5C3A21), Color(0xFF5C3A21)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFD4B899),
          width: 2,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                softWrap: true,
              ),
            ),
            child,
          ],
        ),
      ),
    );
  }
}
