import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Custom RadioGroup and CustomRadioListTile classes (copied from mushaf_screen.dart)
class RadioGroup<T> extends StatefulWidget {
  final T groupValue;
  final ValueChanged<T?> onChanged;
  final List<Widget> children;

  const RadioGroup({
    super.key,
    required this.groupValue,
    required this.onChanged,
    required this.children,
  });

  @override
  State<RadioGroup<T>> createState() => _RadioGroupState<T>();
}

class _RadioGroupState<T> extends State<RadioGroup<T>> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: widget.children,
    );
  }
}

class CustomRadioListTile<T> extends StatelessWidget {
  final String title;
  final T value;
  final T groupValue;
  final ValueChanged<T?> onChanged;
  final Color? textColor; // Added for consistency

  const CustomRadioListTile({
    super.key,
    required this.title,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onChanged(value),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: groupValue == value
              ? Theme.of(context).primaryColor.withAlpha(26)
              : null,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              groupValue == value
                  ? Icons.radio_button_checked
                  : Icons.radio_button_unchecked,
              color: groupValue == value
                  ? Theme.of(context).primaryColor
                  : (textColor ?? Theme.of(context).unselectedWidgetColor),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontWeight:
                      groupValue == value ? FontWeight.bold : FontWeight.normal,
                  color: textColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MushafSettingsScreen extends StatefulWidget {
  const MushafSettingsScreen({super.key});

  @override
  State<MushafSettingsScreen> createState() => _MushafSettingsScreenState();
}

class _MushafSettingsScreenState extends State<MushafSettingsScreen> {
  double _fontSize = 1.0;
  bool _isNightMode = false;
  String _quranEdition = 'hafs';
  bool _isDualPageMode = false;

  final List<String> _quranEditionOptions = [
    'hafs',
    'warsh',
    'douri',
    'qalon',
    'shubah',
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _fontSize = prefs.getDouble('fontSize') ?? 1.0;
      _isNightMode = prefs.getBool('isNightMode') ?? false;
      _quranEdition = prefs.getString('quranEdition') ?? 'hafs';
      _isDualPageMode = prefs.getBool('isDualPageMode') ?? false;
    });
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('fontSize', _fontSize);
    await prefs.setBool('isNightMode', _isNightMode);
    await prefs.setString('quranEdition', _quranEdition);
    await prefs.setBool('isDualPageMode', _isDualPageMode);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إعدادات المصحف الشريف',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF0D47A1), Colors.black],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // إعدادات العرض
            Card(
              color: Colors.black.withAlpha(77),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: const BorderSide(color: Color(0xFFFFD700), width: 1),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات العرض',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text(
                        'وضع القراءة الليلي',
                        style: TextStyle(color: Colors.white),
                      ),
                      value: _isNightMode,
                      onChanged: (value) {
                        setState(() {
                          _isNightMode = value;
                        });
                      },
                      activeTrackColor: const Color(0xFF2E7D32),
                    ),
                    const Text(
                      'حجم الخط',
                      style: TextStyle(color: Colors.white70),
                    ),
                    Slider(
                      value: _fontSize,
                      min: 0.8,
                      max: 1.5,
                      divisions: 7,
                      label: '${(_fontSize * 100).round()}%',
                      activeColor: const Color(0xFF2E7D32),
                      inactiveColor: Colors.white30,
                      onChanged: (value) {
                        setState(() {
                          _fontSize = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'نسخة المصحف',
                      style: TextStyle(color: Colors.white70),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      initialValue: _quranEdition,
                      dropdownColor: Colors.black87,
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: Colors.black.withAlpha(51),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: Colors.white30),
                        ),
                      ),
                      items: _quranEditionOptions.map((String edition) {
                        return DropdownMenuItem<String>(
                          value: edition,
                          child: Text(edition),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _quranEdition = value!;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text(
                        'عرض صفحتين متجاورتين',
                        style: TextStyle(color: Colors.white),
                      ),
                      value: _isDualPageMode,
                      onChanged: (value) {
                        setState(() {
                          _isDualPageMode = value;
                        });
                      },
                      activeTrackColor: const Color(0xFF2E7D32),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // أزرار الحفظ والإلغاء
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                OutlinedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.white),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: const Text(
                    'إلغاء',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    _saveSettings();
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم حفظ الإعدادات بنجاح'),
                        backgroundColor: Color(0xFF2E7D32),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2E7D32),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: const Text('حفظ'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
