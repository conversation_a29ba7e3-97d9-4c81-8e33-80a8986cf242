import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:hijri/hijri_calendar.dart';
import 'package:adhan/adhan.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:async';

import 'data/verses.dart';
import 'features/prayer_times.dart';
import 'features/quran_new.dart';
import 'features/azkar.dart';
import 'features/asma_ul_husna.dart';
import 'features/hadith.dart';
import 'features/qibla_screen.dart';
import 'features/splash_screen.dart';
import 'features/islamic_calendar.dart';
import 'features/dhikr_counter.dart';
import 'features/home/<USER>/daily_hadith.dart';
import 'features/home/<USER>/feature_card.dart';
import 'features/home/<USER>/morning_evening_azkar.dart';
import 'features/home/<USER>/header_image.dart';
import 'features/home/<USER>/hijri_date.dart';
import 'features/home/<USER>/next_prayer_time.dart';
import 'features/home/<USER>/verse_of_the_day.dart';
import 'features/settings_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:man_yakhaf_waeid/themes.dart';

final appFontNotifier = ValueNotifier<String>('Amiri');
final themeNotifier = ValueNotifier<ThemeData>(lightTheme);

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final prefs = await SharedPreferences.getInstance();
  appFontNotifier.value = prefs.getString('appFont') ?? 'Amiri';
  final theme = prefs.getString('theme') ?? 'light';
  themeNotifier.value = theme == 'light' ? lightTheme : darkTheme;
  runApp(const IslamicApp());
}

class IslamicApp extends StatelessWidget {
  const IslamicApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<ThemeData>(
        valueListenable: themeNotifier,
        builder: (context, theme, child) {
          return ValueListenableBuilder<String>(
            valueListenable: appFontNotifier,
            builder: (context, appFont, child) {
              return MaterialApp(
                title: 'من يخاف وعيد',
                theme: theme,
                locale: const Locale('ar'), // Set the locale to Arabic
                supportedLocales: const [
                  Locale('ar', ''), // Arabic
                  Locale('en', ''), // English
                ],
                localizationsDelegates: const [
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                home: const SplashScreen(),
                debugShowCheckedModeBanner: false,
              );
            },
          );
        });
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  PrayerTimes? prayerTimes;
  Timer? _timer;
  Map<String, String>? _verseOfTheDay;

  @override
  void initState() {
    super.initState();
    _getPrayerTimes();
    _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _getPrayerTimes();
    });
    _setVerseOfTheDay();
  }

  void _setVerseOfTheDay() {
    final dayOfYear =
        DateTime.now().difference(DateTime(DateTime.now().year, 1, 1)).inDays;
    final verseIndex = dayOfYear % verses.length;
    setState(() {
      _verseOfTheDay = verses[verseIndex];
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _getPrayerTimes() async {
    try {
      Position position = await _determinePosition();
      final myCoordinates = Coordinates(position.latitude, position.longitude);
      final params = CalculationMethod.muslim_world_league.getParameters();
      params.madhab = Madhab.shafi;
      final prayerTimes = PrayerTimes.today(myCoordinates, params);
      if (mounted) {
        setState(() {
          this.prayerTimes = prayerTimes;
        });
      }
    } catch (e) {
      // Handle location errors gracefully
    }
  }

  Future<Position> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return Future.error('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    return await Geolocator.getCurrentPosition();
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    // The list of pages to be displayed by the BottomNavigationBar
    final List<Widget> pages = <Widget>[
      _buildMainContent(),
      // We use a key to ensure the QuranScreen is rebuilt when navigating
      // This is a placeholder, ideally you'd have a more robust navigation
      QuranScreen(key: UniqueKey(), surahName: 'الفاتحة', surahNumber: 1),
      const HadithScreen(),
      const SettingsScreen(),
    ];

    return Scaffold(
      body: IndexedStack(
        index: _selectedIndex,
        children: pages,
      ),
      bottomNavigationBar: BottomNavigationBar(
        items: <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Image.asset(
              'assets/icons/house.png',
              width: 24,
              height: 24,
            ),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Image.asset(
              'assets/icons/read-quran.png',
              width: 24,
              height: 24,
            ),
            label: 'القرآن',
          ),
          BottomNavigationBarItem(
            icon: Image.asset(
              'assets/icons/islamic-book.png',
              width: 24,
              height: 24,
            ),
            label: 'الحديث',
          ),
          BottomNavigationBarItem(
            icon: Image.asset(
              'assets/icons/settings.png',
              width: 24,
              height: 24,
            ),
            label: 'الإعدادات',
          ),
        ],
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        type: BottomNavigationBarType.fixed,
        backgroundColor: themeNotifier.value.scaffoldBackgroundColor,
        selectedItemColor: themeNotifier.value.colorScheme.primary,
        unselectedItemColor: const Color(0xFF5C3A21),
        showUnselectedLabels: true,
      ),
    );
  }

  Widget _buildMainContent() {
    final today = HijriCalendar.now();
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF5C3A21), Color(0xFF5C3A21)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 200.0,
            floating: false,
            pinned: true,
            elevation: 0,
            backgroundColor: Colors.transparent,
            flexibleSpace: const FlexibleSpaceBar(
              background: HeaderImage(),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  HijriDate(today: today),
                  const SizedBox(height: 20),
                  NextPrayerTime(prayerTimes: prayerTimes),
                  const SizedBox(height: 20),
                  VerseOfTheDay(verseOfTheDay: _verseOfTheDay),
                  const SizedBox(height: 20),
                  const MorningEveningAzkar(),
                  const SizedBox(height: 20),
                  const DailyHadith(),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
          SliverPadding(
            padding: const EdgeInsets.all(16.0),
            sliver: SliverGrid.count(
              crossAxisCount: 3,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                FeatureCard(
                  title: 'أوقات الصلاة',
                  iconWidget: Image.asset(
                    'assets/icons/prayer_times_icon.png',
                    width: 30,
                    height: 30,
                  ),
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const PrayerTimesScreen()),
                  ),
                ),
                FeatureCard(
                  title: 'القرآن الكريم',
                  iconWidget: Image.asset(
                    'assets/icons/quran_icon.png',
                    width: 30,
                    height: 30,
                  ),
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const QuranScreen(
                              surahName: 'الفاتحة',
                              surahNumber: 1,
                            )),
                  ),
                ),
                FeatureCard(
                  title: 'الأذكار والأدعية',
                  iconWidget: Image.asset(
                    'assets/icons/dua_icon.png',
                    width: 30,
                    height: 30,
                  ),
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const AzkarScreen()),
                  ),
                ),
                FeatureCard(
                  title: 'أسماء الله الحسنى',
                  iconWidget: Image.asset(
                    'assets/icons/allah_names_icon.png',
                    width: 30,
                    height: 30,
                  ),
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const AsmaUlHusnaScreen()),
                  ),
                ),
                FeatureCard(
                  title: 'الحديث',
                  iconWidget: Image.asset(
                    'assets/icons/hadith_icon.png',
                    width: 30,
                    height: 30,
                  ),
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const HadithScreen()),
                  ),
                ),
                FeatureCard(
                  title: 'اتجاه القبلة',
                  iconWidget: Image.asset(
                    'assets/icons/qibla_icon.png',
                    width: 30,
                    height: 30,
                  ),
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const QiblaScreen()),
                  ),
                ),
                FeatureCard(
                  title: 'التقويم الإسلامي',
                  iconWidget: Image.asset(
                    'assets/icons/calendar_icon.png',
                    width: 30,
                    height: 30,
                  ),
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const IslamicCalendarScreen()),
                  ),
                ),
                FeatureCard(
                  title: 'مسبحة',
                  iconWidget: Image.asset(
                    'assets/icons/dhikr_icon.png',
                    width: 30,
                    height: 30,
                  ),
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const DhikrCounterScreen()),
                  ),
                ),
                FeatureCard(
                  title: 'الإعدادات',
                  iconWidget: Image.asset(
                    'assets/icons/settings_icon.png',
                    width: 30,
                    height: 30,
                  ),
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const SettingsScreen()),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
