import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class TranslationScreen extends StatefulWidget {
  final int surahNumber;
  final int ayahNumber;
  final String translationName;

  const TranslationScreen({
    super.key,
    required this.surahNumber,
    required this.ayahNumber,
    required this.translationName,
  });

  @override
  State<TranslationScreen> createState() => _TranslationScreenState();
}

class _TranslationScreenState extends State<TranslationScreen> {
  Map<String, dynamic> translationData = {};
  bool isLoading = true;
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadTranslation();
  }

  Future<void> _loadTranslation() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = '';
      });

      // تحديد رمز الترجمة بناءً على الاسم
      int translationId = 20; // افتراضي (إنجليزي)
      switch (widget.translationName) {
        case 'English_Sahih':
          translationId = 20;
          break;
        case 'French_Hamidullah':
          translationId = 33;
          break;
        case 'Spanish_Garcia':
          translationId = 142;
          break;
        case 'Indonesian_Sabiq':
          translationId = 33;
          break;
        case 'Turkish_Yasar':
          translationId = 77;
          break;
        case 'Urdu_Junagarhi':
          translationId = 54;
          break;
      }

      final response = await http.get(
        Uri.parse(
            'https://api.quran.com/api/v4/quran/translations/${widget.surahNumber}:${widget.ayahNumber}?translations=$translationId'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          translationData = data['translations'][0];
          isLoading = false;
        });
      } else {
        setState(() {
          errorMessage = 'خطأ في تحميل الترجمة';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'خطأ: ${e.toString()}';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'ترجمة سورة ${widget.surahNumber}:${widget.ayahNumber}',
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF5C3A21),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF5C3A21), Color(0xFF5C3A21)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: isLoading
            ? const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF5C3A21)),
                ),
              )
            : errorMessage.isNotEmpty
                ? Center(
                    child: Text(
                      errorMessage,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF5C3A21),
                      ),
                    ),
                  )
                : SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Card(
                      color: const Color(0xFF5C3A21).withOpacity(0.3),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                        side: const BorderSide(
                            color: Color(0xFF5C3A21), width: 1),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // اسم الترجمة
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              decoration: BoxDecoration(
                                color: const Color(0xFF5C3A21).withOpacity(0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'ترجمة ${widget.translationName.replaceAll('_', ' ')}',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            const SizedBox(height: 16),
                            // نص الترجمة
                            Text(
                              translationData['text'] ?? 'لا توجد ترجمة متاحة',
                              style: const TextStyle(
                                fontSize: 18,
                                height: 1.8,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.justify,
                              textDirection: TextDirection.ltr,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
      ),
    );
  }
}
