import 'package:flutter/material.dart';

class HadithScreen extends StatefulWidget {
  const HadithScreen({super.key});

  @override
  State<HadithScreen> createState() => _HadithScreenState();
}

class _HadithScreenState extends State<HadithScreen> {
  final List<Map<String, dynamic>> hadithCategories = [
    {
      'title': 'الأربعون النووية',
      'description': 'أربعون حديثاً من جوامع الكلم',
      'icon': Icons.menu_book,
      'color': const Color(0xFFF57F17),
      'hadiths': [
        {
          'number': 1,
          'text':
              'عن أمير المؤمنين أبي حفص عمر بن الخطاب رضي الله عنه قال: سمعت رسول الله صلى الله عليه وسلم يقول: "إنما الأعمال بالنيات، وإنما لكل امرئ ما نوى، فمن كانت هجرته إلى الله ورسوله فهجرته إلى الله ورسوله، ومن كانت هجرته لدنيا يصيبها أو امرأة ينكحها فهجرته إلى ما هاجر إليه"',
          'narrator': 'عمر بن الخطاب',
          'source': 'البخاري ومسلم',
          'lesson': 'أهمية النية في الأعمال'
        },
        {
          'number': 2,
          'text':
              'عن عمر رضي الله عنه أيضاً قال: "بينما نحن جلوس عند رسول الله صلى الله عليه وسلم ذات يوم إذ طلع علينا رجل شديد بياض الثياب، شديد سواد الشعر، لا يُرى عليه أثر السفر، ولا يعرفه منا أحد، حتى جلس إلى النبي صلى الله عليه وسلم فأسند ركبتيه إلى ركبتيه ووضع كفيه على فخذيه وقال: يا محمد أخبرني عن الإسلام..."',
          'narrator': 'عمر بن الخطاب',
          'source': 'مسلم',
          'lesson': 'تعريف الإسلام والإيمان والإحسان'
        },
        {
          'number': 3,
          'text':
              'عن أبي عبد الرحمن عبد الله بن عمر بن الخطاب رضي الله عنهما قال: سمعت رسول الله صلى الله عليه وسلم يقول: "بُني الإسلام على خمس: شهادة أن لا إله إلا الله وأن محمداً رسول الله، وإقام الصلاة، وإيتاء الزكاة، وحج البيت، وصوم رمضان"',
          'narrator': 'عبد الله بن عمر',
          'source': 'البخاري ومسلم',
          'lesson': 'أركان الإسلام الخمسة'
        },
      ]
    },
    {
      'title': 'أحاديث الرقائق',
      'description': 'أحاديث ترقق القلب وتزيد الإيمان',
      'icon': Icons.favorite,
      'color': const Color(0xFF7B1FA2),
      'hadiths': [
        {
          'number': 1,
          'text':
              'عن أبي هريرة رضي الله عنه قال: قال رسول الله صلى الله عليه وسلم: "الدنيا سجن المؤمن وجنة الكافر"',
          'narrator': 'أبو هريرة',
          'source': 'مسلم',
          'lesson': 'حقيقة الدنيا للمؤمن والكافر'
        },
        {
          'number': 2,
          'text':
              'عن ابن عمر رضي الله عنهما قال: أخذ رسول الله صلى الله عليه وسلم بمنكبي فقال: "كن في الدنيا كأنك غريب أو عابر سبيل"',
          'narrator': 'عبد الله بن عمر',
          'source': 'البخاري',
          'lesson': 'الزهد في الدنيا والاستعداد للآخرة'
        },
      ]
    },
    {
      'title': 'أحاديث الآداب',
      'description': 'أحاديث في الآداب والأخلاق الإسلامية',
      'icon': Icons.handshake,
      'color': const Color(0xFF2E7D32),
      'hadiths': [
        {
          'number': 1,
          'text':
              'عن أبي هريرة رضي الله عنه قال: قال رسول الله صلى الله عليه وسلم: "المسلم من سلم المسلمون من لسانه ويده"',
          'narrator': 'أبو هريرة',
          'source': 'البخاري ومسلم',
          'lesson': 'صفات المسلم الحق'
        },
        {
          'number': 2,
          'text':
              'عن أبي موسى الأشعري رضي الله عنه قال: قال رسول الله صلى الله عليه وسلم: "على كل مسلم صدقة" قيل: أرأيت إن لم يجد؟ قال: "يعمل بيده فينفع نفسه ويتصدق" قيل: أرأيت إن لم يستطع؟ قال: "يعين ذا الحاجة الملهوف"',
          'narrator': 'أبو موسى الأشعري',
          'source': 'البخاري ومسلم',
          'lesson': 'أنواع الصدقة والعمل الخيري'
        },
      ]
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'الأحاديث النبوية',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF0D47A1), Colors.black],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: hadithCategories.length,
          itemBuilder: (context, index) {
            final category = hadithCategories[index];
            return GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => HadithCategoryScreen(
                      title: category['title'],
                      hadiths: category['hadiths'],
                      color: category['color'],
                    ),
                  ),
                );
              },
              child: Container(
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF0D47A1), Colors.black],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: const Color(0xFFFFD700), // Gold
                    width: 2,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF0D47A1).withValues(alpha: 0.8),
                              Colors.black.withValues(alpha: 0.8)
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Icon(
                          category['icon'],
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(height: 8),
                      OutlinedButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => HadithCategoryScreen(
                                title: category['title'],
                                hadiths: category['hadiths'],
                                color: category['color'],
                              ),
                            ),
                          );
                        },
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(
                              color: Colors.greenAccent, width: 2),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30.0),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                        ),
                        child: Text(
                          category['title'],
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class HadithCategoryScreen extends StatelessWidget {
  final String title;
  final List<Map<String, dynamic>> hadiths;
  final Color color;

  const HadithCategoryScreen({
    super.key,
    required this.title,
    required this.hadiths,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: color,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [color.withValues(alpha: 0.1), const Color(0xFFF5F5F5)],
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: hadiths.length,
          itemBuilder: (context, index) {
            final hadith = hadiths[index];
            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'حديث ${hadith['number']}',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    hadith['text'],
                    style: const TextStyle(
                      fontSize: 16,
                      height: 2.0,
                      color: Color(0xFF2E7D32),
                    ),
                    textAlign: TextAlign.right,
                    textDirection: TextDirection.rtl,
                  ),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Text(
                              'الراوي: ${hadith['narrator']}',
                              style: TextStyle(
                                fontSize: 12,
                                color: color,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Text(
                              'المصدر: ${hadith['source']}',
                              style: TextStyle(
                                fontSize: 12,
                                color: color,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.lightbulb,
                                color: color,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  hadith['lesson'],
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: color,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.right,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
