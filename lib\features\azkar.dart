import 'package:flutter/material.dart';

class AzkarScreen extends StatefulWidget {
  const AzkarScreen({super.key});

  @override
  State<AzkarScreen> createState() => _AzkarScreenState();
}

class _AzkarScreenState extends State<AzkarScreen> {
  final List<Map<String, dynamic>> azkarCategories = [
    {
      'title': 'أذكار الصباح',
      'icon': Icons.wb_sunny,
      'color': const Color(0xFFFF8F00),
      'azkar': [
        {
          'text':
              'أَعُوذُ بِاللهِ مِنْ الشَّيْطَانِ الرَّجِيمِ اللّهُ لاَ إِلَـهَ إِلاَّ هُوَ الْحَيُّ الْقَيُّومُ لاَ تَأْخُذُهُ سِنَةٌ وَلاَ نَوْمٌ لَّهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الأَرْضِ مَن ذَا الَّذِي يَشْفَعُ عِنْدَهُ إِلاَّ بِإِذْنِهِ يَعْلَمُ مَا بَيْنَ أَيْدِيهِمْ وَمَا خَلْفَهُمْ وَلاَ يُحِيطُونَ بِشَيْءٍ مِّنْ عِلْمِهِ إِلاَّ بِمَا شَاء وَسِعَ كُرْسِيُّهُ السَّمَاوَاتِ وَالأَرْضَ وَلاَ يَؤُودُهُ حِفْظُهُمَا وَهُوَ الْعَلِيُّ الْعَظِيمُ',
          'count': 1,
          'benefit': 'من قرأها حين يصبح أجير من الجن حتى يمسي'
        },
        {
          'text':
              'بِسْمِ اللهِ الرَّحْمنِ الرَّحِيم قُلْ هُوَ اللَّهُ أَحَدٌ، اللَّهُ الصَّمَدُ، لَمْ يَلِدْ وَلَمْ يُولَدْ، وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ',
          'count': 3,
          'benefit': 'من قالها حين يصبح وحين يمسي كفته من كل شيء'
        },
        {
          'text':
              'اللَّهُمَّ بِكَ أَصْبَحْنَا وَبِكَ أَمْسَيْنَا وَبِكَ نَحْيَا وَبِكَ نَمُوتُ وَإِلَيْكَ النُّشُورُ',
          'count': 1,
          'benefit': 'دعاء جامع للخير'
        },
      ]
    },
    {
      'title': 'أذكار المساء',
      'icon': Icons.nights_stay,
      'color': const Color(0xFF7B1FA2),
      'azkar': [
        {
          'text':
              'أَعُوذُ بِاللهِ مِنْ الشَّيْطَانِ الرَّجِيمِ اللّهُ لاَ إِلَـهَ إِلاَّ هُوَ الْحَيُّ الْقَيُّومُ لاَ تَأْخُذُهُ سِنَةٌ وَلاَ نَوْمٌ لَّهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الأَرْضِ مَن ذَا الَّذِي يَشْفَعُ عِنْدَهُ إِلاَّ بِإِذْنِهِ يَعْلَمُ مَا بَيْنَ أَيْدِيهِمْ وَمَا خَلْفَهُمْ وَلاَ يُحِيطُونَ بِشَيْءٍ مِّنْ عِلْمِهِ إِلاَّ بِمَا شَاء وَسِعَ كُرْسِيُّهُ السَّمَاوَاتِ وَالأَرْضَ وَلاَ يَؤُودُهُ حِفْظُهُمَا وَهُوَ الْعَلِيُّ الْعَظِيمُ',
          'count': 1,
          'benefit': 'من قرأها حين يمسي أجير من الجن حتى يصبح'
        },
        {
          'text':
              'اللَّهُمَّ بِكَ أَمْسَيْنَا وَبِكَ أَصْبَحْنَا وَبِكَ نَحْيَا وَبِكَ نَمُوتُ وَإِلَيْكَ الْمَصِيرُ',
          'count': 1,
          'benefit': 'دعاء جامع للخير'
        },
        {
          'text':
              'أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ، لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ',
          'count': 1,
          'benefit': 'تسبيح وتحميد لله'
        },
      ]
    },
    {
      'title': 'أذكار بعد الصلاة',
      'icon': Icons.mosque,
      'color': const Color(0xFF2E7D32),
      'azkar': [
        {
          'text': 'أَسْتَغْفِرُ اللهَ',
          'count': 3,
          'benefit': 'الاستغفار بعد الصلاة'
        },
        {
          'text':
              'اللَّهُمَّ أَنْتَ السَّلاَمُ وَمِنْكَ السَّلاَمُ، تَبَارَكْتَ يَا ذَا الْجَلاَلِ وَالإِكْرَامِ',
          'count': 1,
          'benefit': 'دعاء بعد السلام من الصلاة'
        },
        {
          'text': 'سُبْحَانَ اللهِ',
          'count': 33,
          'benefit': 'التسبيح بعد الصلاة'
        },
        {
          'text': 'الْحَمْدُ للهِ',
          'count': 33,
          'benefit': 'التحميد بعد الصلاة'
        },
        {
          'text': 'اللهُ أَكْبَرُ',
          'count': 34,
          'benefit': 'التكبير بعد الصلاة'
        },
      ]
    },
    {
      'title': 'أذكار النوم',
      'icon': Icons.bedtime,
      'color': const Color(0xFF512DA8),
      'azkar': [
        {
          'text':
              'بِاسْمِكَ رَبِّي وَضَعْتُ جَنْبِي، وَبِكَ أَرْفَعُهُ، فَإِن أَمْسَكْتَ نَفْسِي فَارْحَمْهَا، وَإِنْ أَرْسَلْتَهَا فَاحْفَظْهَا بِمَا تَحْفَظُ بِهِ عِبَادَكَ الصَّالِحِينَ',
          'count': 1,
          'benefit': 'دعاء عند النوم'
        },
        {
          'text':
              'اللَّهُمَّ إِنَّكَ خَلَقْتَ نَفْسِي وَأَنْتَ تَوَفَّاهَا لَكَ مَمَاتُهَا وَمَحْيَاهَا، إِنْ أَحْيَيْتَهَا فَاحْفَظْهَا، وَإِنْ أَمَتَّهَا فَاغْفِرْ لَهَا. اللَّهُمَّ إِنِّي أَسْأَلُكَ الْعَافِيَةَ',
          'count': 1,
          'benefit': 'دعاء شامل عند النوم'
        },
      ]
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'الأذكار والأدعية',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF0D47A1), Colors.black],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: azkarCategories.length,
          itemBuilder: (context, index) {
            final category = azkarCategories[index];
            return GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AzkarDetailScreen(
                      title: category['title'],
                      azkar: category['azkar'],
                      color: category['color'],
                    ),
                  ),
                );
              },
              child: Container(
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF0D47A1), Colors.black],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: const Color(0xFFFFD700), // Gold
                    width: 2,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF0D47A1).withValues(alpha: 0.8),
                              Colors.black.withValues(alpha: 0.8)
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Icon(
                          category['icon'],
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(height: 8),
                      OutlinedButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => AzkarDetailScreen(
                                title: category['title'],
                                azkar: category['azkar'],
                                color: category['color'],
                              ),
                            ),
                          );
                        },
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(
                              color: Colors.greenAccent, width: 2),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30.0),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                        ),
                        child: Text(
                          category['title'],
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class AzkarDetailScreen extends StatefulWidget {
  final String title;
  final List<Map<String, dynamic>> azkar;
  final Color color;

  const AzkarDetailScreen({
    super.key,
    required this.title,
    required this.azkar,
    required this.color,
  });

  @override
  State<AzkarDetailScreen> createState() => _AzkarDetailScreenState();
}

class _AzkarDetailScreenState extends State<AzkarDetailScreen> {
  Map<int, int> counters = {};

  @override
  void initState() {
    super.initState();
    for (int i = 0; i < widget.azkar.length; i++) {
      counters[i] = widget.azkar[i]['count'];
    }
  }

  void _decrementCounter(int index) {
    setState(() {
      if (counters[index]! > 0) {
        counters[index] = counters[index]! - 1;
      }
    });
  }

  void _resetCounter(int index) {
    setState(() {
      counters[index] = widget.azkar[index]['count'];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: widget.color,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                for (int i = 0; i < widget.azkar.length; i++) {
                  counters[i] = widget.azkar[i]['count'];
                }
              });
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [widget.color.withValues(alpha: 0.1), const Color(0xFFF5F5F5)],
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: widget.azkar.length,
          itemBuilder: (context, index) {
            final zikr = widget.azkar[index];
            final counter = counters[index]!;
            final isCompleted = counter == 0;

            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color:
                    isCompleted ? Colors.green.withValues(alpha: 0.1) : Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: isCompleted
                    ? Border.all(color: Colors.green, width: 2)
                    : null,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          IconButton(
                            onPressed: () => _resetCounter(index),
                            icon: Icon(
                              Icons.refresh,
                              color: widget.color,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: isCompleted
                                  ? Colors.green
                                  : widget.color.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              isCompleted ? 'مكتمل' : '$counter',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color:
                                    isCompleted ? Colors.white : widget.color,
                              ),
                            ),
                          ),
                        ],
                      ),
                      if (isCompleted)
                        const Icon(
                          Icons.check_circle,
                          color: Colors.green,
                          size: 24,
                        ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  GestureDetector(
                    onTap: () => _decrementCounter(index),
                    child: Text(
                      zikr['text'],
                      style: TextStyle(
                        fontSize: 16,
                        height: 2.0,
                        color: isCompleted
                            ? Colors.green
                            : const Color(0xFF2E7D32),
                        fontWeight:
                            isCompleted ? FontWeight.bold : FontWeight.normal,
                      ),
                      textAlign: TextAlign.right,
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: widget.color.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      zikr['benefit'],
                      style: TextStyle(
                        fontSize: 12,
                        color: widget.color,
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.right,
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
