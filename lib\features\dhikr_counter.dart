import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:audioplayers/audioplayers.dart';

class DhikrCounterScreen extends StatefulWidget {
  const DhikrCounterScreen({super.key});

  @override
  State<DhikrCounterScreen> createState() => _DhikrCounterScreenState();
}

class _DhikrCounterScreenState extends State<DhikrCounterScreen> {
  int _counter = 0;
  String _selectedDhikr = 'سبحان الله';
  bool _vibrateEnabled = true;
  bool _soundEnabled = true;
  String _beadStyle = 'circle';
  final AudioPlayer _audioPlayer = AudioPlayer();

  // قائمة الأذكار المتاحة
  final List<String> _dhikrOptions = [
    'سبحان الله',
    'الحمد لله',
    'لا إله إلا الله',
    'الله أكبر',
    'لا حول ولا قوة إلا بالله',
    'أستغفر الله العظيم',
  ];

  // أشكال الخرز المتاحة
  final List<String> _beadStyles = [
    'circle',
    'square',
    'diamond',
  ];

  @override
  void initState() {
    super.initState();
    _loadPreferences();
  }

  // تحميل الإعدادات المحفوظة
  void _loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _counter = prefs.getInt('dhikr_counter') ?? 0;
      _selectedDhikr = prefs.getString('selected_dhikr') ?? 'سبحان الله';
      _vibrateEnabled = prefs.getBool('vibrate_enabled') ?? true;
      _soundEnabled = prefs.getBool('sound_enabled') ?? true;
      _beadStyle = prefs.getString('bead_style') ?? 'circle';
    });
  }

  // حفظ الإعدادات
  void _savePreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('dhikr_counter', _counter);
    await prefs.setString('selected_dhikr', _selectedDhikr);
    await prefs.setBool('vibrate_enabled', _vibrateEnabled);
    await prefs.setBool('sound_enabled', _soundEnabled);
    await prefs.setString('bead_style', _beadStyle);
  }

  void _incrementCounter() {
    setState(() {
      _counter++;
    });

    // تشغيل الاهتزاز إذا كان مفعلًا
    if (_vibrateEnabled) {
      // هنا يمكن إضافة كود الاهتزاز
      // HapticFeedback.lightImpact();
    }

    // تشغيل الصوت إذا كان مفعلًا
    if (_soundEnabled) {
      _playClickSound();
    }

    // حفظ العدد الجديد
    _savePreferences();
  }

  void _resetCounter() {
    setState(() {
      _counter = 0;
    });
    _savePreferences();
  }

  // تشغيل صوت النقر
  void _playClickSound() async {
    try {
      await _audioPlayer.play(AssetSource('sounds/click.mp3'));
    } catch (e) {
      // استخدام debugPrint بدلاً من print في الإنتاج
      debugPrint('خطأ في تشغيل الصوت: $e');
    }
  }

  // تغيير نوع الذكر
  void _changeDhikr(String? newDhikr) {
    if (newDhikr != null) {
      setState(() {
        _selectedDhikr = newDhikr;
        _counter = 0; // إعادة تعيين العداد عند تغيير الذكر
      });
      _savePreferences();
    }
  }

  // تغيير شكل الخرزة
  void _changeBeadStyle(String? newStyle) {
    if (newStyle != null) {
      setState(() {
        _beadStyle = newStyle;
      });
      _savePreferences();
    }
  }

  // تبديل الاهتزاز
  void _toggleVibration(bool value) {
    setState(() {
      _vibrateEnabled = value;
    });
    _savePreferences();
  }

  // تبديل الصوت
  void _toggleSound(bool value) {
    setState(() {
      _soundEnabled = value;
    });
    _savePreferences();
  }

  // الحصول على شكل الخرزة حسب النوع المختار
  OutlinedBorder _getBeadShape() {
    switch (_beadStyle) {
      case 'square':
        return const RoundedRectangleBorder();
      case 'diamond':
        return const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
        );
      default:
        return const CircleBorder();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'مسبحة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF5C3A21),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          // زر الإعدادات
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              _showSettingsDialog();
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF5C3A21), Color(0xFF5C3A21)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              // عرض الذكر المحدد
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                decoration: BoxDecoration(
                  color: const Color(0xFF5C3A21).withOpacity(0.3),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: DropdownButton<String>(
                  value: _selectedDhikr,
                  dropdownColor: const Color(0xFF5C3A21).withOpacity(0.87),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                  ),
                  underline: Container(),
                  icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
                  items: _dhikrOptions.map((String dhikr) {
                    return DropdownMenuItem<String>(
                      value: dhikr,
                      child: Text(dhikr),
                    );
                  }).toList(),
                  onChanged: _changeDhikr,
                ),
              ),

              const SizedBox(height: 30),

              // عرض العداد
              Text(
                '$_counter',
                style: const TextStyle(
                  fontSize: 100,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 20),

              // أزرار التحكم
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // زر الزيادة
                  ElevatedButton(
                    onPressed: _incrementCounter,
                    style: ElevatedButton.styleFrom(
                      shape: _getBeadShape(),
                      padding: const EdgeInsets.all(24),
                      backgroundColor: const Color(0xFF5C3A21),
                    ),
                    child: const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),

                  const SizedBox(width: 20),

                  // زر إعادة التعيين
                  ElevatedButton(
                    onPressed: _resetCounter,
                    style: ElevatedButton.styleFrom(
                      shape: _getBeadShape(),
                      padding: const EdgeInsets.all(16),
                      backgroundColor: const Color(0xFF5C3A21),
                    ),
                    child: const Icon(
                      Icons.refresh,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 40),

              // مؤشر بصري للتقدم
              Container(
                width: 200,
                height: 10,
                decoration: BoxDecoration(
                  color: const Color(0xFF5C3A21).withOpacity(0.3),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: (_counter % 33) / 33, // إظهار تقدم لكل 33 مرة
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF5C3A21),
                      borderRadius: BorderRadius.circular(5),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 10),

              // نص إرشادي
              Text(
                'الهدف: ${33 - (_counter % 33)}',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // عرض نافذة الإعدادات
  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('إعدادات المسبحة'),
              content: SingleChildScrollView(
                child: ListBody(
                  children: <Widget>[
                    // إعدادات شكل الخرز
                    const Text(
                      'شكل الخرز:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 10),
                    DropdownButton<String>(
                      value: _beadStyle,
                      isExpanded: true,
                      items: _beadStyles.map((String style) {
                        String displayName = '';
                        switch (style) {
                          case 'circle':
                            displayName = 'دائري';
                            break;
                          case 'square':
                            displayName = 'مربع';
                            break;
                          case 'diamond':
                            displayName = 'معين';
                            break;
                        }
                        return DropdownMenuItem<String>(
                          value: style,
                          child: Text(displayName),
                        );
                      }).toList(),
                      onChanged: (newValue) {
                        setState(() {
                          _beadStyle = newValue!;
                        });
                        _changeBeadStyle(newValue);
                      },
                    ),

                    const SizedBox(height: 20),

                    // إعدادات الاهتزاز
                    SwitchListTile(
                      title: const Text('الاهتزاز'),
                      value: _vibrateEnabled,
                      onChanged: (bool value) {
                        setState(() {
                          _vibrateEnabled = value;
                        });
                        _toggleVibration(value);
                      },
                      secondary: const Icon(Icons.vibration),
                    ),

                    // إعدادات الصوت
                    SwitchListTile(
                      title: const Text('الصوت'),
                      value: _soundEnabled,
                      onChanged: (bool value) {
                        setState(() {
                          _soundEnabled = value;
                        });
                        _toggleSound(value);
                      },
                      secondary: const Icon(Icons.volume_up),
                    ),

                    const SizedBox(height: 20),

                    // إعدادات المزامنة
                    ListTile(
                      leading: const Icon(Icons.sync),
                      title: const Text('مزامنة بين الأجهزة'),
                      subtitle: const Text('حفظ التقدم في السحابة'),
                      onTap: () {
                        // هنا يمكن إضافة كود المزامنة
                        Navigator.of(context).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('ستتوفر المزامنة قريباً'),
                          ),
                        );
                      },
                    ),

                    // زر إعادة جميع الإعدادات
                    ListTile(
                      leading:
                          const Icon(Icons.restore, color: Color(0xFF5C3A21)),
                      title: const Text('إعادة جميع الإعدادات',
                          style: TextStyle(color: Color(0xFF5C3A21))),
                      onTap: () {
                        _resetAllSettings();
                        Navigator.of(context).pop();
                      },
                    ),
                  ],
                ),
              ),
              actions: <Widget>[
                TextButton(
                  child: const Text('حفظ'),
                  onPressed: () {
                    _savePreferences();
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: const Text('إلغاء'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  // إعادة جميع الإعدادات
  void _resetAllSettings() {
    setState(() {
      _counter = 0;
      _selectedDhikr = 'سبحان الله';
      _vibrateEnabled = true;
      _soundEnabled = true;
      _beadStyle = 'circle';
    });
    _savePreferences();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تمت إعادة جميع الإعدادات'),
      ),
    );
  }
}
