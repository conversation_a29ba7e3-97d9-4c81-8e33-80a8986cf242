import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class TafseerScreen extends StatefulWidget {
  final int surahNumber;
  final int ayahNumber;
  final String tafseerName;

  const TafseerScreen({
    super.key,
    required this.surahNumber,
    required this.ayahNumber,
    required this.tafseerName,
  });

  @override
  State<TafseerScreen> createState() => _TafseerScreenState();
}

class _TafseerScreenState extends State<TafseerScreen> {
  Map<String, dynamic> tafseerData = {};
  bool isLoading = true;
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadTafseer();
  }

  Future<void> _loadTafseer() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = '';
      });

      // تحديد رمز المفسر بناءً على الاسم
      String tafseerCode = 'ar.muyassar'; // افتراضي
      switch (widget.tafseerName) {
        case 'Ibn_Kat<PERSON>':
          tafseerCode = 'ar.ibnkathir';
          break;
        case 'Al_Jalalayn':
          tafseerCode = 'ar.jalalayn';
          break;
        case 'Al_Wasit':
          tafseerCode = 'ar.wasit';
          break;
        case 'Al_Muyassar':
          tafseerCode = 'ar.muyassar';
          break;
      }

      final response = await http.get(
        Uri.parse(
            'https://api.quran.com/api/v4/quran/tafsirs/${widget.surahNumber}:${widget.ayahNumber}?tafsirs=$tafseerCode'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          tafseerData = data['tafsirs'][0];
          isLoading = false;
        });
      } else {
        setState(() {
          errorMessage = 'خطأ في تحميل التفسير';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'خطأ: ${e.toString()}';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تفسير سورة ${widget.surahNumber}:${widget.ayahNumber}',
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF5C3A21),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF5C3A21), Color(0xFF5C3A21)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: isLoading
            ? const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF5C3A21)),
                ),
              )
            : errorMessage.isNotEmpty
                ? Center(
                    child: Text(
                      errorMessage,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF5C3A21),
                      ),
                    ),
                  )
                : SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Card(
                      color: const Color(0xFF5C3A21).withOpacity(0.3),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                        side: const BorderSide(
                            color: Color(0xFF5C3A21), width: 1),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // اسم المفسر
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              decoration: BoxDecoration(
                                color: const Color(0xFF5C3A21).withOpacity(0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'تفسير ${widget.tafseerName.replaceAll('_', ' ')}',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            const SizedBox(height: 16),
                            // نص التفسير
                            Text(
                              tafseerData['text'] ?? 'لا يوجد تفسير متاح',
                              style: const TextStyle(
                                fontSize: 18,
                                height: 1.8,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.justify,
                              textDirection: TextDirection.rtl,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
      ),
    );
  }
}
