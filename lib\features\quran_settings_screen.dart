import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../data/reciters_data.dart';

class QuranSettingsScreen extends StatefulWidget {
  const QuranSettingsScreen({super.key});

  @override
  State<QuranSettingsScreen> createState() => _QuranSettingsScreenState();
}

class _QuranSettingsScreenState extends State<QuranSettingsScreen> {
  double _quranFontSize = 20.0;
  String _selectedFont = 'Uthmanic';
  bool _showTranslation = false;
  String _selectedTranslation = 'English_Sahih';
  bool _showTafseer = false;
  String _selectedTafseer = 'Ibn_Kathir';
  bool _autoPlay = false;
  bool _offlineMode = false;
  String _selectedReciterBaseUrl = reciters[0]['baseUrl']!;
  String _selectedReciterName = reciters[0]['name']!;

  final List<String> _fontOptions = [
    'Uthmanic',
    'UthmanicHafs',
    'ScheherazadeNew',
    'Amiri',
    'Cairo',
  ];

  final List<String> _translationOptions = [
    'English_Sahih',
    'French_Hamidullah',
    'Spanish_Garcia',
    'Indonesian_Sabiq',
    'Turkish_Yasar',
    'Urdu_Junagarhi',
  ];

  final List<String> _tafseerOptions = [
    'Ibn_Kathir',
    'Al_Jalalayn',
    'Al_Muyassar',
    'Al_Wasit',
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _quranFontSize = prefs.getDouble('quranFontSize') ?? 20.0;
      _selectedFont = prefs.getString('quranFont') ?? 'Uthmanic';
      _showTranslation = prefs.getBool('showTranslation') ?? false;
      _selectedTranslation =
          prefs.getString('selectedTranslation') ?? 'English_Sahih';
      _showTafseer = prefs.getBool('showTafseer') ?? false;
      _selectedTafseer = prefs.getString('selectedTafseer') ?? 'Ibn_Kathir';
      _autoPlay = prefs.getBool('autoPlay') ?? false;
      _offlineMode = prefs.getBool('offlineMode') ?? false;
      _selectedReciterBaseUrl =
          prefs.getString('selectedReciterBaseUrl') ?? reciters[0]['baseUrl']!;
      _selectedReciterName =
          prefs.getString('selectedReciterName') ?? reciters[0]['name']!;
    });
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('quranFontSize', _quranFontSize);
    await prefs.setString('quranFont', _selectedFont);
    await prefs.setBool('showTranslation', _showTranslation);
    await prefs.setString('selectedTranslation', _selectedTranslation);
    await prefs.setBool('showTafseer', _showTafseer);
    await prefs.setString('selectedTafseer', _selectedTafseer);
    await prefs.setBool('autoPlay', _autoPlay);
    await prefs.setBool('offlineMode', _offlineMode);
    await prefs.setString('selectedReciterBaseUrl', _selectedReciterBaseUrl);
    await prefs.setString('selectedReciterName', _selectedReciterName);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إعدادات القرآن',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF0D47A1), Colors.black],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // إعدادات الخط
            Card(
              color: Colors.black.withValues(alpha: 0.3),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: const BorderSide(color: Color(0xFFFFD700), width: 1),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات الخط',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // حجم الخط
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'حجم الخط',
                          style: TextStyle(color: Colors.white70),
                        ),
                        Text(
                          '${_quranFontSize.toInt()}',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                    Slider(
                      value: _quranFontSize,
                      min: 16.0,
                      max: 36.0,
                      divisions: 10,
                      activeColor: const Color(0xFF2E7D32),
                      inactiveColor: Colors.white30,
                      onChanged: (value) {
                        setState(() {
                          _quranFontSize = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    // نوع الخط
                    const Text(
                      'نوع الخط',
                      style: TextStyle(color: Colors.white70),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      initialValue: _selectedFont,
                      dropdownColor: Colors.black87,
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: Colors.black.withValues(alpha: 0.2),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: Colors.white30),
                        ),
                      ),
                      items: _fontOptions.map((String font) {
                        return DropdownMenuItem<String>(
                          value: font,
                          child: Text(font),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedFont = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // إعدادات الترجمة
            Card(
              color: Colors.black.withValues(alpha: 0.3),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: const BorderSide(color: Color(0xFFFFD700), width: 1),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات الترجمة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // عرض الترجمة
                    SwitchListTile(
                      title: const Text(
                        'عرض الترجمة',
                        style: TextStyle(color: Colors.white),
                      ),
                      value: _showTranslation,
                      onChanged: (value) {
                        setState(() {
                          _showTranslation = value;
                        });
                      },
                      activeTrackColor: const Color(0xFF2E7D32),
                    ),
                    if (_showTranslation) ...[
                      const SizedBox(height: 16),
                      const Text(
                        'لغة الترجمة',
                        style: TextStyle(color: Colors.white70),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        initialValue: _selectedTranslation,
                        dropdownColor: Colors.black87,
                        style: const TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: Colors.black.withValues(alpha: 0.2),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(color: Colors.white30),
                          ),
                        ),
                        items: _translationOptions.map((String translation) {
                          return DropdownMenuItem<String>(
                            value: translation,
                            child: Text(translation.replaceAll('_', ' ')),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedTranslation = value!;
                          });
                        },
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // إعدادات التفسير
            Card(
              color: Colors.black.withValues(alpha: 0.3),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: const BorderSide(color: Color(0xFFFFD700), width: 1),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات التفسير',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // عرض التفسير
                    SwitchListTile(
                      title: const Text(
                        'عرض التفسير',
                        style: TextStyle(color: Colors.white),
                      ),
                      value: _showTafseer,
                      onChanged: (value) {
                        setState(() {
                          _showTafseer = value;
                        });
                      },
                      activeTrackColor: const Color(0xFF2E7D32),
                    ),
                    if (_showTafseer) ...[
                      const SizedBox(height: 16),
                      const Text(
                        'مختار التفسير',
                        style: TextStyle(color: Colors.white70),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        initialValue: _selectedTafseer,
                        dropdownColor: Colors.black87,
                        style: const TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: Colors.black.withValues(alpha: 0.2),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(color: Colors.white30),
                          ),
                        ),
                        items: _tafseerOptions.map((String tafseer) {
                          return DropdownMenuItem<String>(
                            value: tafseer,
                            child: Text(tafseer.replaceAll('_', ' ')),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedTafseer = value!;
                          });
                        },
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // إعدادات الصوت
            Card(
              color: Colors.black.withValues(alpha: 0.3),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: const BorderSide(color: Color(0xFFFFD700), width: 1),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات الصوت',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // التشغيل التلقائي
                    SwitchListTile(
                      title: const Text(
                        'التشغيل التلقائي للتلاوة',
                        style: TextStyle(color: Colors.white),
                      ),
                      value: _autoPlay,
                      onChanged: (value) {
                        setState(() {
                          _autoPlay = value;
                        });
                      },
                      activeTrackColor: const Color(0xFF2E7D32),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // إعدادات القارئ
            Card(
              color: Colors.black.withValues(alpha: 0.3),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: const BorderSide(color: Color(0xFFFFD700), width: 1),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات القارئ',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'اختر القارئ',
                      style: TextStyle(color: Colors.white70),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      initialValue: _selectedReciterName,
                      dropdownColor: Colors.black87,
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: Colors.black.withValues(alpha: 0.2),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: Colors.white30),
                        ),
                      ),
                      items: reciters.map((reciter) {
                        return DropdownMenuItem<String>(
                          value: reciter['name'],
                          child: Text(reciter['name']!),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedReciterName = value!;
                          _selectedReciterBaseUrl = reciters.firstWhere(
                              (r) => r['name'] == value)['baseUrl']!;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // إعدادات الاتصال
            Card(
              color: Colors.black.withValues(alpha: 0.3),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: const BorderSide(color: Color(0xFFFFD700), width: 1),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات الاتصال',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // وضع بدون إنترنت
                    SwitchListTile(
                      title: const Text(
                        'وضع القراءة بدون إنترنت',
                        style: TextStyle(color: Colors.white),
                      ),
                      subtitle: const Text(
                        'تحميل المحتوى للاستخدام دون اتصال',
                        style: TextStyle(color: Colors.white70),
                      ),
                      value: _offlineMode,
                      onChanged: (value) {
                        setState(() {
                          _offlineMode = value;
                        });
                      },
                      activeTrackColor: const Color(0xFF2E7D32),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // أزرار الحفظ والإلغاء
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                OutlinedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.white),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: const Text(
                    'إلغاء',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    _saveSettings();
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم حفظ الإعدادات بنجاح'),
                        backgroundColor: Color(0xFF2E7D32),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2E7D32),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: const Text('حفظ'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
