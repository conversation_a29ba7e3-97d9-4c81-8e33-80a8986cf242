import 'package:flutter/material.dart';

class VerseOfTheDay extends StatelessWidget {
  final Map<String, String>? verseOfTheDay;

  const VerseOfTheDay({super.key, required this.verseOfTheDay});

  @override
  Widget build(BuildContext context) {
    if (verseOfTheDay == null) {
      return Container();
    }
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF0D47A1), Colors.black],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFFFD700), // Gold
          width: 2,
        ),
      ),
      child: Column(
        children: [
          const Text(
            'آية اليوم',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '"${verseOfTheDay!['verse']}"',
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '(${verseOfTheDay!['surah']})',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }
}
