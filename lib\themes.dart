import 'package:flutter/material.dart';

final darkTheme = ThemeData(
  fontFamily: '<PERSON><PERSON>',
  primarySwatch: Colors.grey,
  primaryColor: Colors.black,
  brightness: Brightness.dark,
  scaffoldBackgroundColor: const Color(0xFF212121),
  appBarTheme: const AppBarTheme(
    backgroundColor: Color(0xFF0D47A1),
    foregroundColor: Colors.white, // For title and icons
  ),
  colorScheme: ColorScheme.fromSwatch(
          primarySwatch: Colors.blue, brightness: Brightness.dark)
      .copyWith(secondary: Colors.white),
  iconTheme: const IconThemeData(color: Colors.white),
  dividerColor: Colors.black12,
);

final lightTheme = ThemeData(
  fontFamily: '<PERSON>i',
  primarySwatch: Colors.brown, // Using brown as a base for primarySwatch
  primaryColor: const Color(0xFFD4B899), // Light brown/beige from image
  brightness: Brightness.light,
  scaffoldBackgroundColor:
      const Color(0xFFD4B899), // Light brown/beige from image
  appBarTheme: const AppBarTheme(
    backgroundColor: Color(0xFF5C3A21), // Dark brown from image
    foregroundColor: Colors.white, // For title and icons
  ),
  colorScheme: ColorScheme.fromSwatch(
          primarySwatch: Colors.brown, brightness: Brightness.light)
      .copyWith(secondary: const Color(0xFF5C3A21)), // Dark brown from image
  iconTheme:
      const IconThemeData(color: Colors.white), // Keep icons white for contrast
  dividerColor: Colors.brown.shade200, // Adjust divider color for new theme
);
