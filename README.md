# التطبيق الإسلامي الشامل

تطبيق هاتفي إسلامي شامل مطور باستخدام Flutter و Dart، يحتوي على 10 ميزات أساسية مع محتوى حقيقي من مصادر مفتوحة.

## الميزات الرئيسية

### 1. أوقات الصلاة 🕌
- عرض أوقات الصلاة الخمس بناءً على الموقع الحالي
- استخدام API مفتوح المصدر (Aladhan API)
- تحديث تلقائي للأوقات

### 2. القرآن الكريم 📖
- عرض جميع سور القرآن الكريم (114 سورة)
- قراءة الآيات مع ترقيم واضح
- واجهة سهلة الاستخدام للتنقل بين السور

### 3. الأذكار والأدعية 🤲
- أذكار الصباح والمساء
- أذكار بعد الصلاة
- أذكار النوم
- عداد تفاعلي لكل ذكر

### 4. أسماء الله الحسنى ⭐
- عرض الأسماء الحسنى التسعة والتسعين
- شرح معنى كل اسم
- تصميم جذاب مع أرقام الأسماء

### 5. الأحاديث النبوية 📚
- مجموعة من الأحاديث النبوية الصحيحة
- تصنيف الأحاديث حسب الموضوع
- عرض الراوي والمصدر لكل حديث

### 6. القبلة 🧭
- تحديد اتجاه القبلة (قيد التطوير)

### 7. التقويم الهجري 📅
- عرض التاريخ الهجري (قيد التطوير)

### 8. مواقيت الأذان 🔊
- تنبيهات أوقات الصلاة (قيد التطوير)

### 9. مساجد قريبة 📍
- البحث عن المساجد القريبة (قيد التطوير)

### 10. بطاقات تهنئة إسلامية 🎉
- بطاقات للمناسبات الإسلامية (قيد التطوير)

## التقنيات المستخدمة

- **Flutter**: إطار العمل الرئيسي لتطوير التطبيق
- **Dart**: لغة البرمجة
- **HTTP**: للاتصال بالـ APIs
- **Geolocator**: لتحديد الموقع الجغرافي
- **Permission Handler**: لإدارة أذونات التطبيق

## المصادر المفتوحة المستخدمة

- **Aladhan API**: لأوقات الصلاة
- **Al Quran Cloud API**: لنصوص القرآن الكريم
- **بيانات محلية**: للأذكار وأسماء الله الحسنى والأحاديث

## تصميم واجهة المستخدم

- تصميم عصري مع بطاقات مربعة جذابة
- أيقونات دائرية في وسط كل بطاقة
- ألوان متناسقة ومريحة للعين
- دعم اللغة العربية بالكامل
- تجربة مستخدم سلسة وبديهية

## متطلبات التشغيل

- Flutter SDK 3.4.3 أو أحدث
- Dart 3.0 أو أحدث
- Android Studio أو VS Code
- جهاز Android أو iOS للاختبار

## كيفية تشغيل التطبيق

1. تأكد من تثبيت Flutter SDK
2. استنسخ المشروع أو قم بتنزيله
3. افتح Terminal في مجلد المشروع
4. شغل الأمر: `flutter pub get`
5. شغل الأمر: `flutter run`

## الاختبارات

تم اختبار التطبيق بنجاح:
- ✅ تحليل الكود بدون أخطاء
- ✅ اجتياز جميع الاختبارات الوحدة
- ✅ اختبار الواجهات والتنقل
- ✅ اختبار الاتصال بالـ APIs

## الترخيص

هذا التطبيق مطور لأغراض تعليمية وخيرية. يمكن استخدامه وتطويره بحرية.

## المطور

تم تطوير هذا التطبيق باستخدام الذكاء الاصطناعي Manus، مع التركيز على:
- جودة الكود والأداء
- تجربة المستخدم المتميزة
- المحتوى الإسلامي الصحيح
- التصميم الجذاب والعملي

---

**بسم الله الرحمن الرحيم**

*"وَمَا خَلَقْتُ الْجِنَّ وَالْإِنسَ إِلَّا لِيَعْبُدُونِ"*

