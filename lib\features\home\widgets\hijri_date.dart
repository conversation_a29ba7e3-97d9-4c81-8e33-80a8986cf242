import 'package:flutter/material.dart';
import 'package:hijri/hijri_calendar.dart';

class HijriDate extends StatelessWidget {
  final HijriCalendar today;

  const HijriDate({super.key, required this.today});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF0D47A1), Colors.black],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFFFD700), // Gold
          width: 2,
        ),
      ),
      child: Text(
        today.toFormat("dd MMMM yyyy"),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
