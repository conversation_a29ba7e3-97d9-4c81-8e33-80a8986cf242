import 'package:flutter/material.dart';

class DailyHadith extends StatelessWidget {
  const DailyHadith({super.key});

  @override
  Widget build(BuildContext context) {
    // قائمة أحاديث مؤقتة - يمكن استبدالها بقاعدة بيانات حقيقية
    final List<Map<String, String>> hadiths = [
      {
        "text":
            "قال رسول الله صلى الله عليه وسلم: إنما الأعمال بالنيات، وإنما لكل امرئ ما نوى",
        "source": "متفق عليه"
      },
      {
        "text":
            "قال رسول الله صلى الله عليه وسلم: من قال لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير، في يوم مائة مرة، كانت له عدل عشر رقاب، وكتبت له مائة حسنة، ومحيت عنه مائة سيئة، وكانت له حرزا من الشيطان يومه ذلك حتى يمسي",
        "source": "متفق عليه"
      },
      {
        "text":
            "قال رسول الله صلى الله عليه وسلم: كلمتان خفيفتان على اللسان، ثقيلتان في الميزان، حبيبتان إلى الرحمن: سبحان الله وبحمده، سبحان الله العظيم",
        "source": "متفق عليه"
      }
    ];

    // اختيار حديث عشوائي
    final random = DateTime.now().day % hadiths.length;
    final hadith = hadiths[random];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF0D47A1), Colors.black],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFFFD700), // Gold
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.menu_book,
                color: Color(0xFFFFD700),
                size: 24,
              ),
              SizedBox(width: 10),
              Text(
                'حديث اليوم',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Text(
            hadith["text"]!,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.white,
              height: 1.5,
            ),
            textAlign: TextAlign.right,
          ),
          const SizedBox(height: 10),
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFFFFD700).withAlpha(51),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFFFFD700),
                  width: 1,
                ),
              ),
              child: Text(
                hadith["source"]!,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFFFD700),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
