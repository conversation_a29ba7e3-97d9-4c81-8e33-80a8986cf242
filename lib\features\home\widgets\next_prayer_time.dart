import 'package:adhan/adhan.dart';
import 'package:flutter/material.dart';

class NextPrayerTime extends StatelessWidget {
  final PrayerTimes? prayerTimes;

  const NextPrayerTime({super.key, required this.prayerTimes});

  @override
  Widget build(BuildContext context) {
    if (prayerTimes == null) {
      return const CircularProgressIndicator();
    }

    final nextPrayer = prayerTimes!.nextPrayer();
    final prayerTime = prayerTimes!.timeForPrayer(nextPrayer);
    final difference =
        prayerTime?.difference(DateTime.now()) ?? const Duration();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF0D47A1), Colors.black],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFFFD700), // Gold
          width: 2,
        ),
      ),
      child: Column(
        children: [
          Text(
            'صلاة ${nextPrayer.toString().split('.').last} بعد',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${difference.inHours}:${(difference.inMinutes % 60).toString().padLeft(2, '0')}:${(difference.inSeconds % 60).toString().padLeft(2, '0')}',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
