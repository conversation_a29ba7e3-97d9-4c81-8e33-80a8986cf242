import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../data/surah_data.dart';
import 'mushaf_settings_screen.dart'; // Import the new settings screen

class QuranSearchDelegate extends SearchDelegate<String> {
  final Function(String) onSearch;
  final bool _isAdvancedSearch = false;

  QuranSearchDelegate(this.onSearch);

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
          onSearch(query);
        },
      ),
      IconButton(
        icon: const Icon(Icons.tune),
        onPressed: () {
          // فتح خيارات البحث المتقدم
          _showAdvancedSearchOptions(context);
        },
        tooltip: 'خيارات البحث المتقدم',
      ),
    ];
  }

  void _showAdvancedSearchOptions(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('خيارات البحث المتقدم'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Column(
                children: [
                  // Using CustomRadioListTile from mushaf_settings_screen.dart
                  CustomRadioListTile<String>(
                    title: 'بحث نصي',
                    value: 'text',
                    groupValue: _isAdvancedSearch ? 'advanced' : 'text',
                    onChanged: (value) {
                      Navigator.pop(context);
                    },
                  ),
                  CustomRadioListTile<String>(
                    title: 'بحث حسب السورة',
                    value: 'surah',
                    groupValue: _isAdvancedSearch ? 'advanced' : 'text',
                    onChanged: (value) {
                      Navigator.pop(context);
                    },
                  ),
                  CustomRadioListTile<String>(
                    title: 'بحث حسب الجزء',
                    value: 'juz',
                    groupValue: _isAdvancedSearch ? 'advanced' : 'text',
                    onChanged: (value) {
                      Navigator.pop(context);
                    },
                  ),
                  CustomRadioListTile<String>(
                    title: 'بحث حسب الحجة',
                    value: 'hizb',
                    groupValue: _isAdvancedSearch ? 'advanced' : 'text',
                    onChanged: (value) {
                      Navigator.pop(context);
                    },
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    onSearch(query);
    return const Center(child: CircularProgressIndicator());
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    List<String> suggestions = [];

    if (query.isNotEmpty) {
      // اقتراحات بناءً على البحث
      if (query.contains("بسم")) {
        suggestions = ["بسم الله الرحمن الرحيم"];
      } else if (query.contains("الله")) {
        suggestions = ["الله", "الله أكبر", "لا إله إلا الله"];
      } else if (query.contains("الرحمن")) {
        suggestions = ["الرحمن", "الرحمن الرحيم"];
      } else {
        suggestions = ["اقتراحات للبحث: $query"];
      }
    }

    return ListView.builder(
      itemCount: suggestions.length,
      itemBuilder: (context, index) {
        final suggestion = suggestions[index];
        return ListTile(
          title: Text(suggestion),
          onTap: () {
            query = suggestion;
            onSearch(query);
            showResults(context);
          },
        );
      },
    );
  }
}

class MushafScreen extends StatefulWidget {
  const MushafScreen({super.key});

  @override
  State<MushafScreen> createState() => _MushafScreenState();
}

class _MushafScreenState extends State<MushafScreen> {
  PageController? _pageController;
  int _currentPage = 0;
  final int _totalPages = 604;
  int? _bookmarkedPage;

  // Settings that directly affect the Mushaf display
  bool _isNightMode = false;
  double _fontSize = 1.0;
  bool _isDualPageMode = false;
  String _quranEdition = 'hafs';

  List<int> _searchResults = [];
  int _currentSearchIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadSettings(); // Load all settings, including display settings
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _bookmarkedPage = prefs.getInt('bookmark');
      _currentPage = _bookmarkedPage ?? 0;
      _isNightMode = prefs.getBool('isNightMode') ?? false;
      _fontSize = prefs.getDouble('fontSize') ?? 1.0;
      _isDualPageMode = prefs.getBool('isDualPageMode') ?? false;
      _quranEdition = prefs.getString('quranEdition') ?? 'hafs';

      if (_pageController == null) {
        _pageController = PageController(initialPage: _currentPage);
      } else {
        _pageController!.jumpToPage(_currentPage);
      }
    });
  }

  Future<void> _saveBookmark(int page) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('bookmark', page);
    setState(() {
      _bookmarkedPage = page;
    });
  }

  Future<void> _removeBookmark() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('bookmark');
    setState(() {
      _bookmarkedPage = null;
    });
  }

  Future<void> _searchInQuran(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
      });
      return;
    }

    setState(() {
      _searchResults = [];
    });

    // محاكاة البحث في المصحف
    // في التطبيق الحقيقي، سيتم البحث في قاعدة بيانات تحتوي على نص المصحف
    await Future.delayed(const Duration(milliseconds: 500));

    // نتائج البحث الفعلية
    List<int> results = [];

    // البحث عن كلمة محددة
    if (query.length > 2) {
      // محاكاة لنتائج البحث
      if (query.contains("بسم")) {
        results = [1, 2, 3, 50, 100];
      } else if (query.contains("الله")) {
        results = [1, 5, 10, 15, 20, 30, 40, 50];
      } else if (query.contains("الرحمن")) {
        results = [1, 2, 3, 55, 56, 57];
      } else if (query.contains("الرحيم")) {
        results = [1, 2, 3, 10, 20, 30, 40, 50];
      } else if (query.contains("المؤمن")) {
        results = [23, 33, 47, 49, 57, 59, 60, 64];
      } else if (query.contains("الكافر")) {
        results = [2, 16, 39, 40, 46, 48, 57, 58, 59, 60, 64];
      } else {
        // نتائج عشوائية للتوضيح
        for (int i = 0; i < 10; i++) {
          results.add((i * 60) % _totalPages);
        }
      }

      // إزالة التكرارات وترتيب النتائج
      results = results.toSet().toList();
      results.sort();
    }

    setState(() {
      _searchResults = results;
      _currentSearchIndex = 0;
      if (results.isNotEmpty) {
        _pageController?.jumpToPage(results[_currentSearchIndex]);
      }
    });
  }

  void _showSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MushafSettingsScreen(),
      ),
    ).then((_) {
      _loadSettings(); // Reload all settings when returning from settings screen
    });
  }

  void _showSurahIndex() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('فهرس القرآن الكريم'),
          content: DefaultTabController(
            length: 3,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const TabBar(
                  tabs: [
                    Tab(text: 'السور'),
                    Tab(text: 'الأجزاء'),
                    Tab(text: 'الحُجُم'),
                  ],
                ),
                const SizedBox(height: 10),
                SizedBox(
                  height: 300,
                  width: double.maxFinite,
                  child: TabBarView(
                    children: [
                      // فهرس السور
                      ListView.builder(
                        itemCount: surahData.length,
                        itemBuilder: (context, index) {
                          final surah = surahData[index];
                          return ListTile(
                            title: Text(surah.name),
                            trailing: Text('صفحة ${surah.page}'),
                            onTap: () {
                              Navigator.of(context).pop();
                              _pageController?.jumpToPage(surah.page - 1);
                            },
                          );
                        },
                      ),
                      // فهرس الأجزاء
                      ListView.builder(
                        itemCount: 30,
                        itemBuilder: (context, index) {
                          final juz = index + 1;
                          return ListTile(
                            title: Text('الجزء $juz'),
                            trailing: Text('صفحة ${(juz * 20).toString()}'),
                            onTap: () {
                              Navigator.of(context).pop();
                              _pageController?.jumpToPage(juz * 20);
                            },
                          );
                        },
                      ),
                      // فهرس الحُجُم
                      ListView.builder(
                        itemCount: 60,
                        itemBuilder: (context, index) {
                          final hizb = index + 1;
                          return ListTile(
                            title: Text(
                                'الحُجْم $hizb${(hizb % 4 == 0) ? ' (ربع جزء)' : ''}'),
                            trailing: Text('صفحة ${(hizb * 10).toString()}'),
                            onTap: () {
                              Navigator.of(context).pop();
                              _pageController?.jumpToPage(hizb * 10);
                            },
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المصحف الشريف'),
        actions: [
          IconButton(
            icon: Image.asset(
              'assets/icons/search_icon.png',
              width: 24,
              height: 24,
            ),
            onPressed: () {
              showSearch(
                context: context,
                delegate: QuranSearchDelegate(_searchInQuran),
              );
            },
            tooltip: 'البحث في القرآن',
          ),
          IconButton(
            icon: Image.asset(
              'assets/icons/articles_blog_icon.png',
              width: 24,
              height: 24,
            ),
            onPressed: _showSurahIndex,
            tooltip: 'الفهرس',
          ),
          IconButton(
            icon: Image.asset(
              'assets/icons/settings_icon.png',
              width: 24,
              height: 24,
            ),
            onPressed: _showSettings,
            tooltip: 'الإعدادات',
          ),
          IconButton(
            icon: Image.asset(
              'assets/icons/favorites_icon.png',
              width: 24,
              height: 24,
              color: _bookmarkedPage == _currentPage
                  ? Colors.yellow
                  : Colors.white,
            ),
            onPressed: () {
              if (_bookmarkedPage == _currentPage) {
                _removeBookmark();
              } else {
                _saveBookmark(_currentPage);
              }
            },
            tooltip: 'حفظ علامة',
          ),
        ],
      ),
      body: _pageController == null
          ? const Center(child: CircularProgressIndicator())
          : PageView.builder(
              reverse:
                  true, // This will reverse the page order and swipe direction
              controller: _pageController,
              itemCount: _totalPages,
              onPageChanged: (int page) {
                setState(() {
                  _currentPage = page;
                });
              },
              itemBuilder: (context, index) {
                return Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: _isNightMode ? Colors.black87 : Colors.white,
                      ),
                      child: Center(
                        child: _isDualPageMode && index < _totalPages - 1
                            ? Row(
                                children: [
                                  Expanded(
                                    child: Transform.scale(
                                      scale: _fontSize,
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Image.asset(
                                          'assets/images/pages-$_quranEdition/${index + 1}.png',
                                          fit: BoxFit.contain,
                                          color: _isNightMode
                                              ? Colors.white
                                              : null,
                                          colorBlendMode: _isNightMode
                                              ? BlendMode.modulate
                                              : BlendMode.srcOver,
                                        ),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Transform.scale(
                                      scale: _fontSize,
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Image.asset(
                                          'assets/images/pages-$_quranEdition/${index + 2}.png',
                                          fit: BoxFit.contain,
                                          color: _isNightMode
                                              ? Colors.white
                                              : null,
                                          colorBlendMode: _isNightMode
                                              ? BlendMode.modulate
                                              : BlendMode.srcOver,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              )
                            : Transform.scale(
                                scale: _fontSize,
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Image.asset(
                                    'assets/images/pages-$_quranEdition/${index + 1}.png',
                                    fit: BoxFit.contain,
                                    color: _isNightMode ? Colors.white : null,
                                    colorBlendMode: _isNightMode
                                        ? BlendMode.modulate
                                        : BlendMode.srcOver,
                                  ),
                                ),
                              ),
                      ),
                    ),
                  ],
                );
              },
            ),
      bottomNavigationBar: BottomAppBar(
        color: _isNightMode ? Colors.black87 : const Color(0xFF0D47A1),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                    onPressed: _currentPage > 0
                        ? () {
                            _pageController?.previousPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.ease,
                            );
                          }
                        : null,
                  ),
                  Text(
                    'صفحة ${_currentPage + 1}',
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                  ),
                  if (_searchResults.isNotEmpty &&
                      _searchResults.contains(_currentPage))
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.yellow,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'نتيجة ${_searchResults.indexOf(_currentPage) + 1}/${_searchResults.length}',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  IconButton(
                    icon: const Icon(Icons.arrow_forward_ios,
                        color: Colors.white),
                    onPressed: _currentPage < _totalPages - 1
                        ? () {
                            _pageController?.nextPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.ease,
                            );
                          }
                        : null,
                  ),
                ],
              ),
              Slider(
                activeColor: Colors.white,
                inactiveColor: Colors.white54,
                value: _currentPage.toDouble(),
                min: 0,
                max: (_totalPages - 1).toDouble(),
                divisions: _totalPages - 1,
                label: 'صفحة ${_currentPage + 1}',
                onChanged: (double value) {
                  _pageController?.jumpToPage(value.toInt());
                },
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  IconButton(
                    icon: Image.asset(
                      'assets/icons/settings_icon.png',
                      width: 24,
                      height: 24,
                    ),
                    onPressed: _showSettings,
                    tooltip: 'الإعدادات',
                  ),
                  IconButton(
                    icon: Image.asset(
                      'assets/icons/search_icon.png',
                      width: 24,
                      height: 24,
                    ),
                    onPressed: () {
                      showSearch(
                        context: context,
                        delegate: QuranSearchDelegate(_searchInQuran),
                      );
                    },
                    tooltip: 'البحث',
                  ),
                  IconButton(
                    icon: Image.asset(
                      'assets/icons/favorites_icon.png',
                      width: 24,
                      height: 24,
                    ),
                    onPressed: () {
                      if (_bookmarkedPage == _currentPage) {
                        _removeBookmark();
                      } else {
                        _saveBookmark(_currentPage);
                      }
                    },
                    tooltip: 'حفظ العلامة',
                  ),
                  IconButton(
                    icon: Image.asset(
                      'assets/icons/articles_blog_icon.png',
                      width: 24,
                      height: 24,
                    ),
                    onPressed: _showSurahIndex,
                    tooltip: 'الفهرس',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
